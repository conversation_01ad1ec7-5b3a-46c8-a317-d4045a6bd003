# Docker Compose Security Configuration for Wiz-Aroma
# This file extends the main docker-compose.yml with enhanced security features

version: '3.8'

services:
  # Main application with enhanced security
  wiz-aroma-app:
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
      - /var/tmp:noexec,nosuid,size=50m
    volumes:
      # Override with read-only mounts where possible
      - wiz-aroma-logs:/app/logs:rw
      - wiz-aroma-data:/app/data_files:rw
      - ${FIREBASE_CREDENTIALS_FILE}:/app/firebase-credentials.json:ro
      # Add writable temp directory
      - wiz-aroma-tmp:/app/tmp:rw
    environment:
      # Security-focused environment variables
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
      - PYTHONHASHSEED=random
      - PYTHONPATH=/app
    ulimits:
      nproc: 65535
      nofile:
        soft: 65535
        hard: 65535
    sysctls:
      - net.core.somaxconn=1024

  # Redis with security hardening
  redis:
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=50m
    command: >
      redis-server
      --appendonly yes
      --requirepass ${REDIS_PASSWORD:-wizaroma2024}
      --maxmemory 128mb
      --maxmemory-policy allkeys-lru
      --tcp-keepalive 60
      --timeout 300
      --tcp-backlog 511
      --databases 1
      --save 900 1
      --save 300 10
      --save 60 10000
      --stop-writes-on-bgsave-error yes
      --rdbcompression yes
      --rdbchecksum yes
      --dir /data
      --logfile ""
      --syslog-enabled no

  # Nginx with security headers and configurations
  nginx:
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
      - CHOWN
      - SETGID
      - SETUID
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=50m
      - /var/cache/nginx:noexec,nosuid,size=50m
      - /var/run:noexec,nosuid,size=10m

# Security-focused volumes
volumes:
  wiz-aroma-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./logs
  wiz-aroma-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data_files
  wiz-aroma-tmp:
    driver: local
    driver_opts:
      type: tmpfs
      device: tmpfs

# Secure network configuration
networks:
  wiz-aroma-network:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: wiz-aroma-br
      com.docker.network.bridge.enable_ip_masquerade: "true"
      com.docker.network.bridge.enable_icc: "false"
      com.docker.network.driver.mtu: 1500
    ipam:
      driver: default
      config:
        - subnet: **********/16
          gateway: **********
    internal: false

# Secrets management (for production use)
secrets:
  firebase_credentials:
    file: ${FIREBASE_CREDENTIALS_FILE}
  redis_password:
    external: true
  telegram_tokens:
    external: true
