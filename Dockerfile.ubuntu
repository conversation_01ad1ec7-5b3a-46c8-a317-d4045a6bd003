# Wiz-Aroma Food Delivery System v2.0 - Ubuntu-based Docker Configuration
# Using locally available Ubuntu image

FROM ubuntu:latest

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app
ENV DEBIAN_FRONTEND=noninteractive

# Update package list and install Python and dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-venv \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && ln -sf /usr/bin/python3 /usr/bin/python \
    && ln -sf /usr/bin/pip3 /usr/bin/pip

# Create non-root user
RUN groupadd -r wizaroma && useradd -r -g wizaroma -d /app -s /bin/bash wizaroma

# Create application directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Create necessary directories
RUN mkdir -p /app/logs /app/data_files /app/src && \
    chown -R wizaroma:wizaroma /app

# Copy application code
COPY --chown=wizaroma:wizaroma . /app/

# Make main.py executable
RUN chmod +x /app/main.py

# Create health check script
RUN echo '#!/usr/bin/env python3\n\
import sys\n\
import os\n\
sys.path.append("/app")\n\
try:\n\
    import telebot\n\
    print("Health check passed - Telegram bot library available")\n\
    exit(0)\n\
except Exception as e:\n\
    print(f"Health check failed: {e}")\n\
    exit(1)\n' > /app/health_check.py && \
    chmod +x /app/health_check.py

# Switch to non-root user
USER wizaroma

# Expose port for monitoring
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python /app/health_check.py || exit 1

# Default command - run all bots
CMD ["python", "main.py", "--bot", "all"]
