"""
Logging utilities for the Wiz Aroma Food Delivery system.
"""

import logging
import os
import sys
import io
import datetime
import re
from typing import Dict, Any, Optional, Union, List, Set

# Log levels
CRITICAL = logging.CRITICAL  # 50
ERROR = logging.ERROR  # 40
WARNING = logging.WARNING  # 30
INFO = logging.INFO  # 20
DEBUG = logging.DEBUG  # 10
TRACE = 5  # Custom level below DEBUG

# Add custom TRACE level
logging.addLevelName(TRACE, "TRACE")


def trace(self, message, *args, **kwargs):
    """
    Log 'message % args' with severity 'TRACE'.
    """
    if self.isEnabledFor(TRACE):
        self._log(TRACE, message, args, **kwargs)


# Add trace method to Logger class
logging.Logger.trace = trace


class LogFilter(logging.Filter):
    """
    Filter for excluding redundant or noisy log messages.
    """

    def __init__(self, exclude_patterns: List[str] = None):
        """Initialize with patterns to exclude from logs."""
        super().__init__()
        self.exclude_patterns = exclude_patterns or []
        self.compiled_patterns = [
            re.compile(pattern) for pattern in self.exclude_patterns
        ]

        # Keep track of recent fee check messages to avoid duplication
        self.recent_fee_checks: Set[str] = set()
        self.max_recent_entries = 100

    def filter(self, record):
        """Filter out noisy or redundant messages."""
        msg = record.getMessage()

        # Filter out redundant delivery fee check messages
        if "Checking fee: area_id=" in msg:
            # Only log first occurrence of each unique fee check
            if msg in self.recent_fee_checks:
                return False

            # Add to recent checks and manage set size
            self.recent_fee_checks.add(msg)
            if len(self.recent_fee_checks) > self.max_recent_entries:
                self.recent_fee_checks.pop()

            # Only show a sample of fee checks (1 in 5)
            # import random
            # return random.random() < 0.2

            # Don't log individual fee checks at all - we'll just show the result
            return False

        # Filter out verbose menu data dumps
        if "menus_data contents:" in msg:
            return False

        # Filter based on configured exclude patterns
        for pattern in self.compiled_patterns:
            if pattern.search(msg):
                return False

        return True


class BotLogger:
    """
    Centralized logger for the bot application.
    """

    def __init__(self, name: str = "bot_logger"):
        """
        Initialize the logger.

        Args:
            name: Logger name
        """
        self.logger = logging.getLogger(name)
        self._setup_logger()

    def _setup_logger(self):
        """Set up the logger with appropriate handlers and formatters."""
        # Configure logging with UTF-8 encoding
        sys.stdout.reconfigure(encoding="utf-8")
        sys.stderr.reconfigure(encoding="utf-8")

        # Create logs directory if it doesn't exist
        os.makedirs("logs", exist_ok=True)

        # Create UTF-8 stream handlers
        utf8_stream_handler = logging.StreamHandler(
            io.TextIOWrapper(sys.stdout.buffer, encoding="utf-8")
        )

        # Create file handlers with date in filename
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        utf8_file_handler = logging.FileHandler(
            f"logs/bot_{today}.log", mode="a", encoding="utf-8"
        )

        # Create error file handler for ERROR and above
        error_file_handler = logging.FileHandler(
            f"logs/errors_{today}.log", mode="a", encoding="utf-8"
        )
        error_file_handler.setLevel(logging.ERROR)

        # Set formatters
        standard_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        detailed_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s"
        )

        # Apply formatters
        utf8_stream_handler.setFormatter(standard_formatter)
        utf8_file_handler.setFormatter(standard_formatter)
        error_file_handler.setFormatter(detailed_formatter)

        # Configure root logger
        self.logger.setLevel(TRACE)

        # Remove any existing handlers
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)

        # Add our UTF-8 handlers
        self.logger.addHandler(utf8_stream_handler)
        self.logger.addHandler(utf8_file_handler)
        self.logger.addHandler(error_file_handler)

        # Add filters to reduce noise
        log_filter = LogFilter(
            exclude_patterns=[
                r"^restaurant_menus type:",
                r"^Retrieved fresh points balance",
                r"^Refreshed \w+ data for \d+ users",
            ]
        )

        utf8_stream_handler.addFilter(log_filter)
        utf8_file_handler.addFilter(log_filter)

        # Set higher log level for API-related errors
        logging.getLogger("telebot").setLevel(logging.WARNING)
        logging.getLogger("urllib3").setLevel(logging.WARNING)

    def trace(self, message: str, *args, **kwargs):
        """Log at TRACE level (below DEBUG)."""
        self.logger.trace(message, *args, **kwargs)

    def debug(self, message: str, *args, **kwargs):
        """Log at DEBUG level."""
        self.logger.debug(message, *args, **kwargs)

    def info(self, message: str, *args, **kwargs):
        """Log at INFO level."""
        self.logger.info(message, *args, **kwargs)

    def warning(self, message: str, *args, **kwargs):
        """Log at WARNING level."""
        self.logger.warning(message, *args, **kwargs)

    def error(self, message: str, *args, **kwargs):
        """Log at ERROR level."""
        self.logger.error(message, *args, **kwargs)

    def critical(self, message: str, *args, **kwargs):
        """Log at CRITICAL level."""
        self.logger.critical(message, *args, **kwargs)

    def log_user_action(
        self,
        user_id: Union[int, str],
        action: str,
        details: Optional[Dict[str, Any]] = None,
    ):
        """
        Log user actions with consistent format.

        Args:
            user_id: Telegram user ID
            action: Action being performed
            details: Additional details about the action
        """
        details_str = f" - {details}" if details else ""
        self.info(f"USER ACTION: User {user_id} - {action}{details_str}")

    def log_bot_action(
        self, bot_name: str, action: str, details: Optional[Dict[str, Any]] = None
    ):
        """
        Log bot actions with consistent format.

        Args:
            bot_name: Name of the bot (user, admin, finance, maintenance)
            action: Action being performed
            details: Additional details about the action
        """
        details_str = f" - {details}" if details else ""
        self.info(f"BOT ACTION: {bot_name} bot - {action}{details_str}")

    def log_data_operation(
        self, operation: str, data_type: str, details: Optional[Dict[str, Any]] = None
    ):
        """
        Log data operations with consistent format.

        Args:
            operation: Operation being performed (load, save, update, delete)
            data_type: Type of data being operated on
            details: Additional details about the operation
        """
        details_str = f" - {details}" if details else ""
        self.debug(f"DATA OPERATION: {operation} {data_type}{details_str}")

    def log_api_call(
        self,
        method: str,
        endpoint: str,
        status: str,
        details: Optional[Dict[str, Any]] = None,
    ):
        """
        Log API calls with consistent format.

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            status: Status of the call (success, failure)
            details: Additional details about the call
        """
        details_str = f" - {details}" if details else ""
        self.debug(f"API CALL: {method} {endpoint} - {status}{details_str}")

    def log_fee_summary(self, area_id: int, location_id: int, fee: int):
        """
        Log a summary of delivery fee lookups instead of individual checks.

        Args:
            area_id: The restaurant area ID
            location_id: The delivery location ID
            fee: The determined delivery fee
        """
        self.info(f"Delivery fee for area {area_id} to location {location_id}: {fee}")


# Create a singleton instance
bot_logger = BotLogger()


def get_logger():
    """Get the singleton logger instance."""
    return bot_logger
