"""
Handler registration utilities for the Wiz Aroma Food Delivery system.
"""

import inspect
import functools
from typing import List, Callable, Dict, Any, Optional, Union
import telebot
from telebot import types

from src.utils.logging_utils import get_logger

# Get the logger
logger = get_logger()


class HandlerRegistry:
    """
    Registry for bot handlers to ensure consistent registration.
    """
    
    def __init__(self):
        """Initialize the registry."""
        self.handlers = {
            "user": [],
            "admin": [],
            "finance": [],
            "maintenance": [],
        }
        self.registered = False
    
    def register_handler(
        self,
        bot_type: str,
        handler: Callable,
        handler_type: str = "message",
        **kwargs
    ):
        """
        Register a handler for a specific bot.
        
        Args:
            bot_type: Type of bot (user, admin, finance, maintenance)
            handler: Handler function
            handler_type: Type of handler (message, callback_query, etc.)
            **kwargs: Additional arguments for the handler
        """
        if bot_type not in self.handlers:
            logger.warning(f"Unknown bot type: {bot_type}")
            return
        
        self.handlers[bot_type].append({
            "handler": handler,
            "type": handler_type,
            "kwargs": kwargs,
        })
        
        logger.debug(f"Registered {handler_type} handler {handler.__name__} for {bot_type} bot")
    
    def register_all_handlers(self, bot_instances: Dict[str, telebot.TeleBot]):
        """
        Register all handlers with their respective bot instances.
        
        Args:
            bot_instances: Dictionary mapping bot types to bot instances
        """
        if self.registered:
            logger.warning("Handlers already registered")
            return
        
        for bot_type, handlers in self.handlers.items():
            if bot_type not in bot_instances:
                logger.warning(f"No bot instance for {bot_type}")
                continue
            
            bot = bot_instances[bot_type]
            
            for handler_info in handlers:
                handler = handler_info["handler"]
                handler_type = handler_info["type"]
                kwargs = handler_info["kwargs"]
                
                try:
                    if handler_type == "message":
                        bot.register_message_handler(handler, **kwargs)
                    elif handler_type == "callback_query":
                        bot.register_callback_query_handler(handler, **kwargs)
                    elif handler_type == "inline":
                        bot.register_inline_handler(handler, **kwargs)
                    else:
                        logger.warning(f"Unknown handler type: {handler_type}")
                except Exception as e:
                    logger.error(f"Error registering {handler.__name__} for {bot_type} bot: {str(e)}")
        
        self.registered = True
        logger.info("All handlers registered successfully")
    
    def clear_registry(self):
        """Clear the registry."""
        self.handlers = {
            "user": [],
            "admin": [],
            "finance": [],
            "maintenance": [],
        }
        self.registered = False
        logger.info("Handler registry cleared")


# Create a singleton instance
handler_registry = HandlerRegistry()


def register_handler(
    bot_type: Union[str, List[str]],
    handler_type: str = "message",
    **kwargs
):
    """
    Decorator to register a handler with the registry.
    
    Args:
        bot_type: Type of bot or list of bot types
        handler_type: Type of handler
        **kwargs: Additional arguments for the handler
    
    Returns:
        Decorated function
    """
    def decorator(func):
        # Register the handler with each bot type
        if isinstance(bot_type, list):
            for bt in bot_type:
                handler_registry.register_handler(bt, func, handler_type, **kwargs)
        else:
            handler_registry.register_handler(bot_type, func, handler_type, **kwargs)
        
        # Return the original function unchanged
        return func
    
    return decorator


def get_registry():
    """Get the singleton registry instance."""
    return handler_registry


def register_all_handlers(bot_instances: Dict[str, telebot.TeleBot]):
    """
    Register all handlers with their respective bot instances.
    
    Args:
        bot_instances: Dictionary mapping bot types to bot instances
    """
    handler_registry.register_all_handlers(bot_instances)
