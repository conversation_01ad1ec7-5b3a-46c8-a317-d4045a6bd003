# Wiz-Aroma Docker Build Script for Windows PowerShell
# This script ensures robust Docker deployment with network retry logic

param(
    [string]$Mode = "basic",
    [switch]$Force,
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Colors for output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Success { Write-ColorOutput Green $args }
function Write-Warning { Write-ColorOutput Yellow $args }
function Write-Error { Write-ColorOutput Red $args }
function Write-Info { Write-ColorOutput Cyan $args }

# Logging function
function Write-Log {
    param($Message, $Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    switch ($Level) {
        "SUCCESS" { Write-Success "[$timestamp] $Message" }
        "WARNING" { Write-Warning "[$timestamp] WARNING: $Message" }
        "ERROR" { Write-Error "[$timestamp] ERROR: $Message" }
        default { Write-Info "[$timestamp] $Message" }
    }
}

# Check prerequisites
function Test-Prerequisites {
    Write-Log "Checking prerequisites..."
    
    # Check Docker
    try {
        $dockerVersion = docker --version
        Write-Log "Docker found: $dockerVersion" "SUCCESS"
    }
    catch {
        Write-Log "Docker is not installed or not running" "ERROR"
        exit 1
    }
    
    # Check Docker Compose
    try {
        $composeVersion = docker compose version
        Write-Log "Docker Compose found: $composeVersion" "SUCCESS"
    }
    catch {
        Write-Log "Docker Compose is not available" "ERROR"
        exit 1
    }
    
    # Check if Docker is running
    try {
        docker info | Out-Null
        Write-Log "Docker daemon is running" "SUCCESS"
    }
    catch {
        Write-Log "Docker daemon is not running. Please start Docker Desktop" "ERROR"
        exit 1
    }
}

# Validate environment
function Test-Environment {
    Write-Log "Validating environment configuration..."
    
    if (-not (Test-Path ".env")) {
        if (Test-Path ".env.docker") {
            Write-Log "Copying .env.docker to .env"
            Copy-Item ".env.docker" ".env"
        }
        else {
            Write-Log ".env file not found" "ERROR"
            exit 1
        }
    }
    
    # Check Firebase credentials
    $firebaseFile = (Get-Content ".env" | Where-Object { $_ -match "^FIREBASE_CREDENTIALS_FILE=" }) -replace "FIREBASE_CREDENTIALS_FILE=", ""
    if (-not (Test-Path $firebaseFile)) {
        Write-Log "Firebase credentials file not found: $firebaseFile" "ERROR"
        exit 1
    }
    
    Write-Log "Environment validation passed" "SUCCESS"
}

# Create necessary directories
function New-Directories {
    Write-Log "Creating necessary directories..."
    
    $directories = @("logs", "data_files", "docker\nginx\ssl", "docker\secrets")
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Log "Created directory: $dir"
        }
    }
}

# Build with retry logic
function Invoke-DockerBuildWithRetry {
    param($MaxRetries = 3)
    
    Write-Log "Building Docker images with retry logic..."
    
    for ($i = 1; $i -le $MaxRetries; $i++) {
        try {
            Write-Log "Build attempt $i of $MaxRetries"
            
            # Build the main application
            docker compose build --no-cache wiz-aroma-app
            
            Write-Log "Docker build completed successfully" "SUCCESS"
            return $true
        }
        catch {
            Write-Log "Build attempt $i failed: $($_.Exception.Message)" "WARNING"
            if ($i -eq $MaxRetries) {
                Write-Log "All build attempts failed" "ERROR"
                return $false
            }
            Write-Log "Retrying in 10 seconds..."
            Start-Sleep -Seconds 10
        }
    }
}

# Start services with retry
function Start-Services {
    param($ComposeFiles = @("docker-compose.yml"))
    
    Write-Log "Starting services..."
    
    $composeArgs = @()
    foreach ($file in $ComposeFiles) {
        $composeArgs += "-f"
        $composeArgs += $file
    }
    
    try {
        # Start services
        & docker compose @composeArgs up -d
        
        Write-Log "Services started successfully" "SUCCESS"
        
        # Wait for services to be healthy
        Write-Log "Waiting for services to become healthy..."
        Start-Sleep -Seconds 30
        
        # Check service status
        $services = docker compose ps --format json | ConvertFrom-Json
        foreach ($service in $services) {
            if ($service.State -eq "running") {
                Write-Log "Service $($service.Name) is running" "SUCCESS"
            }
            else {
                Write-Log "Service $($service.Name) is not running: $($service.State)" "WARNING"
            }
        }
    }
    catch {
        Write-Log "Failed to start services: $($_.Exception.Message)" "ERROR"
        return $false
    }
    
    return $true
}

# Show service status
function Show-Status {
    Write-Log "Service Status:"
    docker compose ps
    
    Write-Log "`nContainer Health:"
    docker compose ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"
    
    Write-Log "`nService Logs (last 20 lines):"
    docker compose logs --tail=20
}

# Main deployment function
function Start-Deployment {
    param($Mode)
    
    Write-Log "Starting Wiz-Aroma Docker deployment in $Mode mode..."
    
    # Test prerequisites
    Test-Prerequisites
    
    # Validate environment
    Test-Environment
    
    # Create directories
    New-Directories
    
    # Build images
    if (-not (Invoke-DockerBuildWithRetry)) {
        Write-Log "Build failed. Exiting." "ERROR"
        exit 1
    }
    
    # Determine compose files based on mode
    $composeFiles = @("docker-compose.yml")
    switch ($Mode) {
        "monitoring" {
            if (Test-Path "docker\monitoring\docker-compose.monitoring.yml") {
                $composeFiles += "docker\monitoring\docker-compose.monitoring.yml"
                Write-Log "Adding monitoring stack"
            }
        }
        "security" {
            if (Test-Path "docker\security\docker-compose.security.yml") {
                $composeFiles += "docker\security\docker-compose.security.yml"
                Write-Log "Adding security hardening"
            }
        }
        "full" {
            if (Test-Path "docker\monitoring\docker-compose.monitoring.yml") {
                $composeFiles += "docker\monitoring\docker-compose.monitoring.yml"
            }
            if (Test-Path "docker\security\docker-compose.security.yml") {
                $composeFiles += "docker\security\docker-compose.security.yml"
            }
            Write-Log "Adding full stack (monitoring + security)"
        }
    }
    
    # Start services
    if (Start-Services -ComposeFiles $composeFiles) {
        Write-Log "Deployment completed successfully!" "SUCCESS"
        Show-Status
        
        Write-Log "`nAccess Information:" "SUCCESS"
        Write-Log "- Application: Running in containers"
        Write-Log "- Logs: docker compose logs -f"
        if ($Mode -eq "monitoring" -or $Mode -eq "full") {
            Write-Log "- Grafana: http://localhost:3000 (admin/admin123)"
            Write-Log "- Prometheus: http://localhost:9090"
        }
    }
    else {
        Write-Log "Deployment failed" "ERROR"
        exit 1
    }
}

# Help function
function Show-Help {
    Write-Host @"
Wiz-Aroma Docker Build Script

Usage: .\docker-build.ps1 [OPTIONS]

Options:
    -Mode <string>     Deployment mode: basic, monitoring, security, full (default: basic)
    -Force             Force rebuild without confirmation
    -Verbose           Enable verbose output

Examples:
    .\docker-build.ps1                    # Basic deployment
    .\docker-build.ps1 -Mode monitoring   # With monitoring stack
    .\docker-build.ps1 -Mode full         # Full stack deployment
    .\docker-build.ps1 -Force             # Force rebuild

Modes:
    basic      - Core application only
    monitoring - Core + Prometheus + Grafana
    security   - Core + security hardening
    full       - Everything (monitoring + security)
"@
}

# Main execution
try {
    if ($args -contains "-h" -or $args -contains "--help") {
        Show-Help
        exit 0
    }
    
    Start-Deployment -Mode $Mode
}
catch {
    Write-Log "Deployment failed with error: $($_.Exception.Message)" "ERROR"
    exit 1
}
