# Prometheus Alert Rules for Wiz-Aroma Food Delivery System
groups:
  - name: wiz-aroma-alerts
    rules:
      # Application Health Alerts
      - alert: WizAromaAppDown
        expr: up{job="wiz-aroma-app"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Wiz-Aroma application is down"
          description: "The Wiz-Aroma application has been down for more than 1 minute."

      - alert: WizAromaHighMemoryUsage
        expr: (container_memory_usage_bytes{name="wiz-aroma-main"} / container_spec_memory_limit_bytes{name="wiz-aroma-main"}) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage in Wiz-Aroma application"
          description: "Memory usage is above 80% for more than 5 minutes."

      - alert: WizAromaHighCPUUsage
        expr: rate(container_cpu_usage_seconds_total{name="wiz-aroma-main"}[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage in Wiz-Aroma application"
          description: "CPU usage is above 80% for more than 5 minutes."

  - name: infrastructure-alerts
    rules:
      # System Resource Alerts
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on host"
          description: "Memory usage is above 85% for more than 5 minutes."

      - alert: HighDiskUsage
        expr: (node_filesystem_size_bytes{fstype!="tmpfs"} - node_filesystem_free_bytes{fstype!="tmpfs"}) / node_filesystem_size_bytes{fstype!="tmpfs"} * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High disk usage"
          description: "Disk usage is above 85% for more than 5 minutes."

      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage on host"
          description: "CPU usage is above 85% for more than 5 minutes."

  - name: service-alerts
    rules:
      # Redis Alerts
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis is down"
          description: "Redis service has been down for more than 1 minute."

      # Nginx Alerts
      - alert: NginxDown
        expr: up{job="nginx"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Nginx is down"
          description: "Nginx service has been down for more than 1 minute."

      # Container Restart Alerts
      - alert: ContainerRestarting
        expr: rate(container_last_seen[5m]) > 0
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Container is restarting frequently"
          description: "Container {{ $labels.name }} is restarting frequently."

  - name: business-logic-alerts
    rules:
      # Custom business logic alerts (to be implemented with application metrics)
      - alert: HighOrderFailureRate
        expr: rate(wiz_aroma_failed_orders_total[5m]) / rate(wiz_aroma_total_orders_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High order failure rate"
          description: "Order failure rate is above 10% for more than 2 minutes."

      - alert: NoOrdersReceived
        expr: rate(wiz_aroma_total_orders_total[10m]) == 0
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "No orders received"
          description: "No orders have been received for more than 10 minutes during business hours."
