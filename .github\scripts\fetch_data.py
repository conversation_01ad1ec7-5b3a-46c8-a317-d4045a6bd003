#!/usr/bin/env python3
"""
Script to fetch data from the Wiz Aroma Delivery Bot and update the repository.
This script is used by the GitHub workflow to sync data from the Railway deployment.
"""

import os
import json
import time
import requests
from pathlib import Path

# Get environment variables
BOT_TOKEN = os.environ.get('BOT_TOKEN')
CHAT_ID = os.environ.get('CHAT_ID')

if not BOT_TOKEN or not CHAT_ID:
    print("Error: BOT_TOKEN and CHAT_ID environment variables must be set")
    exit(1)

# Base URL for Telegram Bot API
BASE_URL = f"https://api.telegram.org/bot{BOT_TOKEN}"

def send_message(chat_id, text):
    """Send a message to a Telegram chat"""
    url = f"{BASE_URL}/sendMessage"
    data = {
        "chat_id": chat_id,
        "text": text
    }
    response = requests.post(url, data=data)
    return response.json()

def get_updates(offset=None, timeout=30):
    """Get updates from Telegram Bot API"""
    url = f"{BASE_URL}/getUpdates"
    params = {
        "timeout": timeout
    }
    if offset:
        params["offset"] = offset
    response = requests.get(url, params=params)
    return response.json()

def get_file(file_id):
    """Get file from Telegram Bot API"""
    url = f"{BASE_URL}/getFile"
    params = {
        "file_id": file_id
    }
    response = requests.get(url, params=params)
    file_path = response.json()["result"]["file_path"]
    file_url = f"https://api.telegram.org/file/bot{BOT_TOKEN}/{file_path}"
    return requests.get(file_url).content

def main():
    print("Starting data sync process...")
    
    # Send command to export data
    print("Sending export_data command to bot...")
    send_message(CHAT_ID, "/export_data")
    
    # Wait for the bot to process and respond
    print("Waiting for bot response...")
    time.sleep(5)
    
    # Get updates to find the document
    max_attempts = 10
    attempt = 0
    latest_update_id = None
    documents = []
    
    while attempt < max_attempts and len(documents) < 1:
        print(f"Checking for updates (attempt {attempt+1}/{max_attempts})...")
        updates = get_updates(offset=latest_update_id)
        
        if updates.get("ok") and updates.get("result"):
            for update in updates["result"]:
                latest_update_id = update["update_id"] + 1
                
                # Check if this update contains a document
                if "message" in update and "document" in update["message"]:
                    if update["message"]["chat"]["id"] == int(CHAT_ID):
                        documents.append(update["message"]["document"])
                        print(f"Found document: {update['message']['document']['file_name']}")
        
        attempt += 1
        if len(documents) < 1:
            print("No documents found yet, waiting...")
            time.sleep(5)
    
    if not documents:
        print("Error: No documents received from bot")
        exit(1)
    
    # Process the documents
    print(f"Processing {len(documents)} documents...")
    
    # Create data_files directory if it doesn't exist
    data_dir = Path("data_files")
    data_dir.mkdir(exist_ok=True)
    
    for doc in documents:
        file_name = doc["file_name"]
        file_id = doc["file_id"]
        
        print(f"Downloading {file_name}...")
        file_content = get_file(file_id)
        
        # Parse the JSON content
        data = json.loads(file_content)
        
        # If this is the main data file or config data, extract and save individual files
        if file_name == "wiz_aroma_data.json" or file_name == "config_data.json":
            if "_file_paths" in data:
                file_paths = data.pop("_file_paths")
                
                # Save each data component to its corresponding file
                for key, path in file_paths.items():
                    if key in data:
                        # Create directory if it doesn't exist
                        file_path = Path(path)
                        file_path.parent.mkdir(exist_ok=True)
                        
                        print(f"Saving {key} data to {path}...")
                        with open(path, 'w', encoding='utf-8') as f:
                            if isinstance(data[key], list):
                                # For areas, restaurants, etc. that are stored as lists in a dict
                                json.dump({key: data[key]}, f, indent=4, ensure_ascii=False)
                            else:
                                json.dump(data[key], f, indent=4, ensure_ascii=False)
        
        # If this is user data or order data, extract and save to individual files
        elif file_name in ["user_data.json", "order_data.json"]:
            for key, value in data.items():
                # Determine the file path based on key
                if key == "user_points":
                    path = "data_files/user_points.json"
                elif key == "user_order_history":
                    path = "data_files/user_order_history.json"
                elif key == "user_names":
                    path = "data_files/user_names.json"
                elif key == "user_phone_numbers":
                    path = "data_files/user_phone_numbers.json"
                elif key == "user_emails":
                    path = "data_files/user_emails.json"
                elif key == "favorite_orders":
                    path = "data_files/favorite_orders.json"
                elif key == "current_orders":
                    path = "data_files/current_orders.json"
                elif key == "order_status":
                    path = "data_files/order_status.json"
                elif key == "pending_admin_reviews":
                    path = "data_files/pending_admin_reviews.json"
                elif key == "admin_remarks":
                    path = "data_files/admin_remarks.json"
                elif key == "awaiting_receipt":
                    path = "data_files/awaiting_receipt.json"
                elif key == "user_order_counts":
                    path = "data_files/user_order_counts.json"
                elif key == "current_order_numbers":
                    path = "data_files/current_order_numbers.json"
                else:
                    continue
                
                # Create directory if it doesn't exist
                file_path = Path(path)
                file_path.parent.mkdir(exist_ok=True)
                
                print(f"Saving {key} data to {path}...")
                with open(path, 'w', encoding='utf-8') as f:
                    json.dump(value, f, indent=4, ensure_ascii=False)
    
    print("Data sync completed successfully!")

if __name__ == "__main__":
    main()
