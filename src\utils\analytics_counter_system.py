"""
Analytics Counter System for Wiz Aroma Food Delivery Management System

Implements proper analytics counters that track and automatically reset:
- Daily counters (reset every day at midnight)
- Weekly counters (reset every Monday)
- Monthly counters (reset on the 1st of each month)

Each period tracks:
- Complete orders count
- Incomplete orders count
- Issue/problem orders count
"""

import datetime
from typing import Dict, Any, Optional
import logging

from src.config import logger
from src.firebase_db import get_data, set_data, update_data


def get_current_time_periods() -> Dict[str, Any]:
    """Get current time period identifiers with Ethiopian timezone"""
    # Get Ethiopian local time (EAT, UTC+3)
    utc_now = datetime.datetime.now(datetime.timezone.utc)
    ethiopia_offset = datetime.timedelta(hours=3)
    ethiopia_now = utc_now + ethiopia_offset
    
    # Calculate period boundaries
    week_start = ethiopia_now - datetime.timedelta(days=ethiopia_now.weekday())
    month_start = ethiopia_now.replace(day=1)
    
    return {
        'current_date': ethiopia_now.strftime('%Y-%m-%d'),
        'current_week_start': week_start.strftime('%Y-%m-%d'),
        'current_month_start': month_start.strftime('%Y-%m-%d'),
        'current_year': ethiopia_now.year,
        'current_month': ethiopia_now.month,
        'current_day': ethiopia_now.day,
        'current_weekday': ethiopia_now.weekday(),  # 0 = Monday
        'timestamp': ethiopia_now.isoformat(),
        'ethiopia_time': ethiopia_now
    }


def get_analytics_counters() -> Dict[str, Any]:
    """Get current analytics counters from Firebase"""
    try:
        counters = get_data("analytics_counters") or {}
        
        # Initialize default structure if not exists
        if not counters:
            current_periods = get_current_time_periods()
            counters = {
                'daily': {
                    'period_start': current_periods['current_date'],
                    'complete_orders': 0,
                    'incomplete_orders': 0,
                    'issue_orders': 0,
                    'last_updated': current_periods['timestamp']
                },
                'weekly': {
                    'period_start': current_periods['current_week_start'],
                    'complete_orders': 0,
                    'incomplete_orders': 0,
                    'issue_orders': 0,
                    'last_updated': current_periods['timestamp']
                },
                'monthly': {
                    'period_start': current_periods['current_month_start'],
                    'complete_orders': 0,
                    'incomplete_orders': 0,
                    'issue_orders': 0,
                    'last_updated': current_periods['timestamp']
                },
                'last_reset_check': current_periods['timestamp']
            }
            set_data("analytics_counters", counters)
            
        return counters
        
    except Exception as e:
        logger.error(f"Error getting analytics counters: {e}")
        return {}


def should_reset_counters() -> Dict[str, bool]:
    """Check which counter periods need to be reset"""
    try:
        counters = get_analytics_counters()
        current_periods = get_current_time_periods()
        
        reset_needed = {
            'daily': False,
            'weekly': False,
            'monthly': False
        }
        
        # Check daily reset (new day)
        daily_period_start = counters.get('daily', {}).get('period_start', '')
        if daily_period_start != current_periods['current_date']:
            reset_needed['daily'] = True
            
        # Check weekly reset (new week - Monday)
        weekly_period_start = counters.get('weekly', {}).get('period_start', '')
        if weekly_period_start != current_periods['current_week_start']:
            reset_needed['weekly'] = True
            
        # Check monthly reset (new month)
        monthly_period_start = counters.get('monthly', {}).get('period_start', '')
        if monthly_period_start != current_periods['current_month_start']:
            reset_needed['monthly'] = True
            
        return reset_needed
        
    except Exception as e:
        logger.error(f"Error checking counter resets: {e}")
        return {'daily': False, 'weekly': False, 'monthly': False}


def reset_analytics_counters(periods_to_reset: Dict[str, bool]) -> bool:
    """Reset analytics counters for specified periods"""
    try:
        if not any(periods_to_reset.values()):
            return True  # Nothing to reset
            
        counters = get_analytics_counters()
        current_periods = get_current_time_periods()
        
        # Create backup of current counters before reset
        backup_data = {
            'reset_timestamp': current_periods['timestamp'],
            'previous_counters': counters.copy(),
            'reset_periods': periods_to_reset
        }
        
        # Store backup
        backup_id = f"counter_reset_{current_periods['current_date']}_{current_periods['timestamp'][:19].replace(':', '-')}"
        set_data(f"analytics_counter_backups/{backup_id}", backup_data)
        
        # Reset counters for specified periods
        if periods_to_reset.get('daily'):
            counters['daily'] = {
                'period_start': current_periods['current_date'],
                'complete_orders': 0,
                'incomplete_orders': 0,
                'issue_orders': 0,
                'last_updated': current_periods['timestamp'],
                'reset_from_previous': counters.get('daily', {}).copy()
            }
            logger.info(f"🔄 Daily analytics counters reset for {current_periods['current_date']}")
            
        if periods_to_reset.get('weekly'):
            counters['weekly'] = {
                'period_start': current_periods['current_week_start'],
                'complete_orders': 0,
                'incomplete_orders': 0,
                'issue_orders': 0,
                'last_updated': current_periods['timestamp'],
                'reset_from_previous': counters.get('weekly', {}).copy()
            }
            logger.info(f"🔄 Weekly analytics counters reset for week starting {current_periods['current_week_start']}")
            
        if periods_to_reset.get('monthly'):
            counters['monthly'] = {
                'period_start': current_periods['current_month_start'],
                'complete_orders': 0,
                'incomplete_orders': 0,
                'issue_orders': 0,
                'last_updated': current_periods['timestamp'],
                'reset_from_previous': counters.get('monthly', {}).copy()
            }
            logger.info(f"🔄 Monthly analytics counters reset for {current_periods['current_month_start']}")
            
        # Update last reset check
        counters['last_reset_check'] = current_periods['timestamp']
        
        # Save updated counters
        success = set_data("analytics_counters", counters)
        
        if success:
            reset_list = [k for k, v in periods_to_reset.items() if v]
            logger.info(f"✅ Analytics counters reset completed for: {', '.join(reset_list)}")
        else:
            logger.error("❌ Failed to save reset analytics counters")
            
        return success
        
    except Exception as e:
        logger.error(f"Error resetting analytics counters: {e}")
        return False


def update_analytics_counter(counter_type: str, period: str) -> bool:
    """
    Update analytics counter for a specific type and period
    
    Args:
        counter_type: 'complete_orders', 'incomplete_orders', or 'issue_orders'
        period: 'daily', 'weekly', or 'monthly'
    """
    try:
        if counter_type not in ['complete_orders', 'incomplete_orders', 'issue_orders']:
            logger.error(f"Invalid counter type: {counter_type}")
            return False
            
        if period not in ['daily', 'weekly', 'monthly']:
            logger.error(f"Invalid period: {period}")
            return False
            
        # Check and execute any needed resets first
        check_and_execute_counter_resets()
        
        # Get current counters
        counters = get_analytics_counters()
        current_periods = get_current_time_periods()
        
        # Update the specific counter
        if period not in counters:
            counters[period] = {
                'period_start': current_periods[f'current_{period.replace("ly", "").replace("y", "")}_start'] if period != 'daily' else current_periods['current_date'],
                'complete_orders': 0,
                'incomplete_orders': 0,
                'issue_orders': 0,
                'last_updated': current_periods['timestamp']
            }
            
        counters[period][counter_type] = counters[period].get(counter_type, 0) + 1
        counters[period]['last_updated'] = current_periods['timestamp']
        
        # Save updated counters
        success = set_data("analytics_counters", counters)
        
        if success:
            logger.debug(f"📊 Updated {period} {counter_type}: {counters[period][counter_type]}")
        else:
            logger.error(f"Failed to update {period} {counter_type}")
            
        return success
        
    except Exception as e:
        logger.error(f"Error updating analytics counter: {e}")
        return False


def check_and_execute_counter_resets() -> Dict[str, bool]:
    """Check and execute any needed analytics counter resets"""
    try:
        reset_needed = should_reset_counters()
        
        if any(reset_needed.values()):
            success = reset_analytics_counters(reset_needed)
            if success:
                return reset_needed
            else:
                return {'daily': False, 'weekly': False, 'monthly': False}
        
        return {'daily': False, 'weekly': False, 'monthly': False}
        
    except Exception as e:
        logger.error(f"Error checking and executing counter resets: {e}")
        return {'daily': False, 'weekly': False, 'monthly': False}


def get_analytics_counter_summary() -> Dict[str, Any]:
    """Get a summary of current analytics counters"""
    try:
        # Ensure resets are checked first
        check_and_execute_counter_resets()
        
        counters = get_analytics_counters()
        current_periods = get_current_time_periods()
        
        summary = {
            'current_time': current_periods['timestamp'],
            'periods': {}
        }
        
        for period in ['daily', 'weekly', 'monthly']:
            period_data = counters.get(period, {})
            total_orders = (
                period_data.get('complete_orders', 0) + 
                period_data.get('incomplete_orders', 0) + 
                period_data.get('issue_orders', 0)
            )
            
            summary['periods'][period] = {
                'period_start': period_data.get('period_start', 'N/A'),
                'complete_orders': period_data.get('complete_orders', 0),
                'incomplete_orders': period_data.get('incomplete_orders', 0),
                'issue_orders': period_data.get('issue_orders', 0),
                'total_orders': total_orders,
                'last_updated': period_data.get('last_updated', 'N/A')
            }
            
        return summary
        
    except Exception as e:
        logger.error(f"Error getting analytics counter summary: {e}")
        return {}
