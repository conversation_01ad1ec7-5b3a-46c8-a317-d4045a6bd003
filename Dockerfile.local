# Wiz-Aroma Food Delivery System v2.0 - Local Development Dockerfile
# This Dockerfile is designed to work with local Python installation

# Use a minimal base image that might be cached locally
FROM python:3.11-slim

# Set build arguments
ARG BUILD_DATE
ARG VERSION=2.0

# Add metadata
LABEL maintainer="Wiz-Aroma Development Team"
LABEL version="${VERSION}"
LABEL description="Wiz-Aroma Food Delivery System - Multi-Bot Telegram Application"
LABEL build-date="${BUILD_DATE}"

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# Create non-root user for security
RUN groupadd -r wizaroma && useradd -r -g wizaroma -d /app -s /bin/bash wizaroma

# Create application directory
WORKDIR /app

# Install system dependencies (minimal)
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies with local fallback
RUN pip install --upgrade pip || echo "Pip upgrade failed, continuing..." && \
    pip install -r requirements.txt || echo "Some packages may have failed, continuing..."

# Create necessary directories
RUN mkdir -p /app/logs /app/data_files /app/src && \
    chown -R wizaroma:wizaroma /app

# Copy application code
COPY --chown=wizaroma:wizaroma . /app/

# Ensure main.py is executable
RUN chmod +x /app/main.py

# Create health check script
RUN echo '#!/usr/bin/env python3\n\
import sys\n\
import os\n\
sys.path.append("/app")\n\
try:\n\
    # Simple health check\n\
    import telebot\n\
    print("Health check passed - dependencies available")\n\
    exit(0)\n\
except Exception as e:\n\
    print(f"Health check failed: {e}")\n\
    exit(1)\n' > /app/health_check.py && \
    chmod +x /app/health_check.py

# Switch to non-root user
USER wizaroma

# Expose port for potential web interface
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python /app/health_check.py || exit 1

# Default command - run all bots
CMD ["python", "main.py", "--bot", "all"]
