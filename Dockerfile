# Wiz-Aroma Food Delivery System v2.0 - Docker Configuration
# Multi-stage build for optimized production image

# Build stage
FROM python:3.11-slim as builder

# Set build arguments
ARG BUILD_DATE
ARG VERSION=2.0

# Add metadata
LABEL maintainer="Wiz-Aroma Development Team"
LABEL version="${VERSION}"
LABEL description="Wiz-Aroma Food Delivery System - Multi-Bot Telegram Application"
LABEL build-date="${BUILD_DATE}"

# Set environment variables for build
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies for building
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --upgrade pip && \
    pip install -r requirements.txt

# Production stage
FROM python:3.11-slim as production

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app
ENV PATH="/opt/venv/bin:$PATH"

# Install runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user for security
RUN groupadd -r wizaroma && useradd -r -g wizaroma -d /app -s /bin/bash wizaroma

# Create application directory
WORKDIR /app

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv

# Create necessary directories
RUN mkdir -p /app/logs /app/data_files /app/src && \
    chown -R wizaroma:wizaroma /app

# Copy application code
COPY --chown=wizaroma:wizaroma . /app/

# Ensure main.py is executable
RUN chmod +x /app/main.py

# Create health check script
RUN echo '#!/bin/bash\n\
import sys\n\
import os\n\
sys.path.append("/app")\n\
try:\n\
    from src.config import logger\n\
    from src.firebase_db import get_data\n\
    # Test Firebase connectivity\n\
    test_data = get_data("system_health")\n\
    print("Health check passed")\n\
    exit(0)\n\
except Exception as e:\n\
    print(f"Health check failed: {e}")\n\
    exit(1)\n' > /app/health_check.py && \
    chmod +x /app/health_check.py

# Switch to non-root user
USER wizaroma

# Expose port for potential web interface (future use)
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python /app/health_check.py || exit 1

# Default command - run all bots
CMD ["python", "main.py", "--bot", "all"]
