"""
Utility functions for delivery personnel earnings management.
Contains core business logic for earnings tracking, weekly resets, and reporting.
"""

import datetime
from typing import Dict, List, Optional, Tuple, Any

from src.config import logger
from src.data_models import DeliveryPersonnelEarnings, delivery_personnel_earnings
from src.data_storage import save_delivery_personnel_earnings, load_delivery_personnel_earnings_data
from src.firebase_db import get_data, set_data, update_data


def get_or_create_personnel_earnings(personnel_id: str) -> DeliveryPersonnelEarnings:
    """Get existing earnings record or create new one for personnel"""
    try:
        # Load fresh data from Firebase
        earnings_data = get_data("delivery_personnel_earnings") or {}
        
        if personnel_id in earnings_data:
            # Load existing earnings
            earnings = DeliveryPersonnelEarnings.from_dict(earnings_data[personnel_id])
        else:
            # Create new earnings record
            earnings = DeliveryPersonnelEarnings(personnel_id)
            
        return earnings
        
    except Exception as e:
        logger.error(f"Error getting/creating earnings for personnel {personnel_id}: {e}")
        # Return new earnings record as fallback
        return DeliveryPersonnelEarnings(personnel_id)


def update_personnel_earnings(personnel_id: str, delivery_fee: float) -> bool:
    """Update earnings for a delivery personnel after completing a delivery"""
    try:
        # Input validation
        if not personnel_id or not isinstance(personnel_id, str):
            logger.error(f"Invalid personnel_id: {personnel_id}")
            return False

        if not isinstance(delivery_fee, (int, float)) or delivery_fee < 0:
            logger.error(f"Invalid delivery_fee: {delivery_fee}")
            return False

        # Get or create earnings record
        earnings = get_or_create_personnel_earnings(personnel_id)
        if not earnings:
            logger.error(f"Failed to get/create earnings record for personnel {personnel_id}")
            return False

        # Add the delivery earning
        earnings.add_delivery_earning(delivery_fee)

        # Save to Firebase with validation
        earnings_data = get_data("delivery_personnel_earnings") or {}
        if not isinstance(earnings_data, dict):
            logger.error("Invalid earnings data structure from Firebase")
            earnings_data = {}

        earnings_data[personnel_id] = earnings.to_dict()

        success = update_data("delivery_personnel_earnings", earnings_data)

        if success:
            # Update in-memory cache
            delivery_personnel_earnings[personnel_id] = earnings.to_dict()
            logger.info(f"Updated earnings for personnel {personnel_id}: +{delivery_fee} birr")
        else:
            logger.error(f"Failed to save earnings update for personnel {personnel_id}")

        return success

    except Exception as e:
        logger.error(f"Error updating earnings for personnel {personnel_id}: {e}")
        return False


def get_personnel_earnings_summary(personnel_id: str) -> Dict[str, Any]:
    """Get earnings summary for a specific personnel"""
    try:
        earnings = get_or_create_personnel_earnings(personnel_id)
        
        return {
            "daily_earnings": earnings.daily_earnings,
            "weekly_earnings": earnings.weekly_earnings,
            "total_lifetime_earnings": earnings.total_lifetime_earnings,
            "completed_deliveries_today": earnings.completed_deliveries_today,
            "completed_deliveries_week": earnings.completed_deliveries_week,
            "last_updated": earnings.last_updated.strftime("%Y-%m-%d %H:%M:%S"),
            "current_week_start": earnings.current_week_start.strftime("%Y-%m-%d"),
        }
        
    except Exception as e:
        logger.error(f"Error getting earnings summary for personnel {personnel_id}: {e}")
        return {
            "daily_earnings": 0.0,
            "weekly_earnings": 0.0,
            "total_lifetime_earnings": 0.0,
            "completed_deliveries_today": 0,
            "completed_deliveries_week": 0,
            "last_updated": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "current_week_start": datetime.datetime.now().strftime("%Y-%m-%d"),
        }


def get_all_personnel_earnings() -> Dict[str, Dict[str, Any]]:
    """Get earnings summary for all personnel"""
    try:
        earnings_data = get_data("delivery_personnel_earnings") or {}
        summaries = {}
        
        for personnel_id in earnings_data:
            summaries[personnel_id] = get_personnel_earnings_summary(personnel_id)
            
        return summaries
        
    except Exception as e:
        logger.error(f"Error getting all personnel earnings: {e}")
        return {}


def reset_weekly_earnings_for_all() -> bool:
    """Reset weekly earnings for all personnel (called on Monday)"""
    try:
        earnings_data = get_data("delivery_personnel_earnings") or {}
        updated_data = {}
        
        for personnel_id, earnings_dict in earnings_data.items():
            earnings = DeliveryPersonnelEarnings.from_dict(earnings_dict)
            
            # Check if reset is needed and perform it
            if earnings.is_new_week():
                earnings.reset_weekly_earnings()
                
            updated_data[personnel_id] = earnings.to_dict()
        
        # Save all updated data
        success = update_data("delivery_personnel_earnings", updated_data)
        
        if success:
            # Update in-memory cache
            delivery_personnel_earnings.clear()
            delivery_personnel_earnings.update(updated_data)
            logger.info("Weekly earnings reset completed for all personnel")
        else:
            logger.error("Failed to save weekly earnings reset")
            
        return success
        
    except Exception as e:
        logger.error(f"Error resetting weekly earnings: {e}")
        return False


def get_weekly_earnings_report() -> Dict[str, Any]:
    """Generate weekly earnings report for all personnel"""
    try:
        # Get current week info
        today = datetime.datetime.now()
        week_start = today - datetime.timedelta(days=today.weekday())
        week_end = week_start + datetime.timedelta(days=6)
        
        # Get all personnel earnings
        all_earnings = get_all_personnel_earnings()
        
        # Calculate totals
        total_weekly_earnings = sum(
            earnings.get("weekly_earnings", 0) for earnings in all_earnings.values()
        )
        total_weekly_deliveries = sum(
            earnings.get("completed_deliveries_week", 0) for earnings in all_earnings.values()
        )
        
        # Get personnel data for names
        personnel_data = get_data("delivery_personnel") or {}
        
        # Create detailed report
        personnel_reports = []
        for personnel_id, earnings in all_earnings.items():
            personnel_info = personnel_data.get(personnel_id, {})
            personnel_reports.append({
                "personnel_id": personnel_id,
                "name": personnel_info.get("name", "Unknown"),
                "weekly_earnings": earnings.get("weekly_earnings", 0),
                "weekly_deliveries": earnings.get("completed_deliveries_week", 0),
                "daily_earnings": earnings.get("daily_earnings", 0),
                "daily_deliveries": earnings.get("completed_deliveries_today", 0),
            })
        
        # Sort by weekly earnings (highest first)
        personnel_reports.sort(key=lambda x: x["weekly_earnings"], reverse=True)
        
        return {
            "week_start": week_start.strftime("%Y-%m-%d"),
            "week_end": week_end.strftime("%Y-%m-%d"),
            "total_weekly_earnings": total_weekly_earnings,
            "total_weekly_deliveries": total_weekly_deliveries,
            "personnel_count": len(personnel_reports),
            "personnel_reports": personnel_reports,
            "average_earnings_per_person": total_weekly_earnings / len(personnel_reports) if personnel_reports else 0,
            "average_deliveries_per_person": total_weekly_deliveries / len(personnel_reports) if personnel_reports else 0,
        }
        
    except Exception as e:
        logger.error(f"Error generating weekly earnings report: {e}")
        return {
            "week_start": "",
            "week_end": "",
            "total_weekly_earnings": 0,
            "total_weekly_deliveries": 0,
            "personnel_count": 0,
            "personnel_reports": [],
            "average_earnings_per_person": 0,
            "average_deliveries_per_person": 0,
        }


def check_and_reset_daily_earnings() -> bool:
    """Check and reset daily earnings for all personnel if new day"""
    try:
        earnings_data = get_data("delivery_personnel_earnings") or {}
        updated_data = {}
        reset_count = 0
        
        for personnel_id, earnings_dict in earnings_data.items():
            earnings = DeliveryPersonnelEarnings.from_dict(earnings_dict)
            
            # Check if reset is needed and perform it
            if earnings.is_new_day():
                earnings.reset_daily_earnings()
                reset_count += 1
                
            updated_data[personnel_id] = earnings.to_dict()
        
        if reset_count > 0:
            # Save updated data
            success = update_data("delivery_personnel_earnings", updated_data)
            
            if success:
                # Update in-memory cache
                delivery_personnel_earnings.clear()
                delivery_personnel_earnings.update(updated_data)
                logger.info(f"Daily earnings reset completed for {reset_count} personnel")
            else:
                logger.error("Failed to save daily earnings reset")
                
            return success
        else:
            # No reset needed
            return True
            
    except Exception as e:
        logger.error(f"Error checking/resetting daily earnings: {e}")
        return False
