# Wiz-Aroma Docker Ignore File
# Excludes unnecessary files from Docker build context

# Version control
.git
.gitignore
.gitattributes

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs and temporary files
logs/
*.log
*.tmp
*.temp
.cache/
.pytest_cache/

# Environment files (security)
.env
.env.local
.env.production
.env.staging

# Firebase credentials (security)
*firebase*.json
*serviceAccount*.json

# Documentation (not needed in container)
*.md
docs/
documentation/

# Docker files (avoid recursion)
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Testing
tests/
test_*.py
*_test.py
coverage/
.coverage
.nyc_output
.tox/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.bak
*.backup
*.old

# Local development files
local/
dev/
development/

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Data files that should be in volumes
data_files/
*.db
*.sqlite
*.sqlite3
