version: '3.8'

services:
  # Main Wiz-Aroma Application
  wiz-aroma-app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        BUILD_DATE: ${BUILD_DATE:-$(date -u +'%Y-%m-%dT%H:%M:%SZ')}
        VERSION: "2.0"
    container_name: wiz-aroma-main
    restart: unless-stopped
    environment:
      # Bot Tokens
      - BOT_TOKEN=${BOT_TOKEN}
      - ADMIN_BOT_TOKEN=${ADMIN_BOT_TOKEN}
      - FINANCE_BOT_TOKEN=${FINANCE_BOT_TOKEN}
      - MAINTENANCE_BOT_TOKEN=${MAINTENANCE_BOT_TOKEN}
      - MANAGEMENT_BOT_TOKEN=${MANAGEMENT_BOT_TOKEN}
      - ORDER_TRACK_BOT_TOKEN=${ORDER_TRACK_BOT_TOKEN}
      - DELIVERY_BOT_TOKEN=${DELIVERY_BOT_TOKEN}
      
      # Firebase Configuration
      - FIREBASE_DATABASE_URL=${FIREBASE_DATABASE_URL}
      - FIREBASE_CREDENTIALS_PATH=/app/firebase-credentials.json
      
      # System Configuration
      - SYSTEM_ADMIN_ID=${SYSTEM_ADMIN_ID}
      - ORDER_TRACK_BOT_AUTHORIZED_IDS=${ORDER_TRACK_BOT_AUTHORIZED_IDS}
      - DELIVERY_BOT_AUTHORIZED_IDS=${DELIVERY_BOT_AUTHORIZED_IDS}
      - MANAGEMENT_BOT_AUTHORIZED_IDS=${MANAGEMENT_BOT_AUTHORIZED_IDS}
      
      # Payment Information
      - TELEBIRR_PHONE=${TELEBIRR_PHONE}
      - TELEBIRR_NAME=${TELEBIRR_NAME}
      - CBE_ACCOUNT_NUMBER=${CBE_ACCOUNT_NUMBER}
      - CBE_ACCOUNT_NAME=${CBE_ACCOUNT_NAME}
      - BOA_ACCOUNT_NUMBER=${BOA_ACCOUNT_NUMBER}
      - BOA_ACCOUNT_NAME=${BOA_ACCOUNT_NAME}
      
      # Contact Information
      - SUPPORT_PHONE_1=${SUPPORT_PHONE_1}
      - SUPPORT_PHONE_2=${SUPPORT_PHONE_2}
      - SUPPORT_TELEGRAM=${SUPPORT_TELEGRAM}
      - CUSTOMER_SERVICE_PHONE=${CUSTOMER_SERVICE_PHONE}
      - CUSTOMER_SERVICE_EMAIL=${CUSTOMER_SERVICE_EMAIL}
      - BUSINESS_EMAIL=${BUSINESS_EMAIL}
      
      # Email Configuration (Optional)
      - EMAIL_ADDRESS=${EMAIL_ADDRESS:-}
      - EMAIL_PASSWORD=${EMAIL_PASSWORD:-}
      
      # Application Settings
      - TEST_MODE=${TEST_MODE:-false}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - PYTHONPATH=/app
    
    volumes:
      # Persistent data storage
      - wiz-aroma-logs:/app/logs
      - wiz-aroma-data:/app/data_files
      # Firebase credentials (bind mount for security)
      - ${FIREBASE_CREDENTIALS_FILE}:/app/firebase-credentials.json:ro
    
    networks:
      - wiz-aroma-network
    
    healthcheck:
      test: ["CMD", "python", "/app/health_check.py"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Redis for caching (optional but recommended for production)
  redis:
    image: redis:7-alpine
    container_name: wiz-aroma-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-wizaroma2024}
    volumes:
      - wiz-aroma-redis:/data
    networks:
      - wiz-aroma-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.1'

  # Nginx reverse proxy (for future web dashboard)
  nginx:
    image: nginx:alpine
    container_name: wiz-aroma-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
      - wiz-aroma-logs:/var/log/nginx
    networks:
      - wiz-aroma-network
    depends_on:
      - wiz-aroma-app
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

# Named volumes for persistent data
volumes:
  wiz-aroma-logs:
    driver: local
  wiz-aroma-data:
    driver: local
  wiz-aroma-redis:
    driver: local

# Custom network for service communication
networks:
  wiz-aroma-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
