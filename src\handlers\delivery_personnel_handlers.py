"""
Delivery Personnel handlers for the Wiz Aroma Delivery Bot.
Contains handlers for delivery personnel registration, management, and assignment.
"""

import datetime
import uuid
from telebot import types
from typing import Dict, List, Optional

from src.bot_instance import admin_bot, maintenance_bot
from src.utils.handler_registration import register_handler
from src.config import logger, ADMIN_CHAT_IDS, MAINTENANCE_CHAT_ID
from src.utils.delivery_personnel_utils import (
    get_capacity_summary,
    validate_all_capacities,
    monitor_capacity_alerts,
    get_real_time_capacity,
    set_personnel_status,
    toggle_personnel_status,
    get_personnel_status,
    get_personnel_by_status,
    get_status_summary,
    validate_all_statuses,
    update_status_based_on_capacity
)
from src.data_models import (
    DeliveryPersonnel,
    DeliveryAssignment,
    delivery_personnel,
    delivery_personnel_assignments,
    delivery_personnel_availability,
    delivery_personnel_capacity,
    delivery_personnel_zones,
    delivery_personnel_performance,
    areas_data,
)
from src.data_storage import (
    save_delivery_personnel,
    save_delivery_personnel_assignments,
    save_delivery_personnel_availability,
    save_delivery_personnel_capacity,
    save_delivery_personnel_zones,
    save_delivery_personnel_performance,
    load_delivery_personnel_data,
    load_delivery_personnel_assignments_data,
    load_delivery_personnel_availability_data,
    load_delivery_personnel_capacity_data,
    load_delivery_personnel_zones_data,
    load_delivery_personnel_performance_data,
)


# Temporary storage for registration process
personnel_registration_data = {}


@register_handler("admin", commands=["delivery_personnel"])
def delivery_personnel_menu(message):
    """Show delivery personnel management menu"""
    try:
        # Check if user is authorized
        if str(message.from_user.id) not in ADMIN_CHAT_IDS:
            admin_bot.reply_to(message, "⚠️ You are not authorized to use this command.")
            return

        markup = types.InlineKeyboardMarkup(row_width=2)
        markup.add(
            types.InlineKeyboardButton("👥 View All Personnel", callback_data="dp_view_all"),
            types.InlineKeyboardButton("➕ Register New", callback_data="dp_register"),
        )
        markup.add(
            types.InlineKeyboardButton("📊 Personnel Status", callback_data="dp_status"),
            types.InlineKeyboardButton("🗺️ Zone Management", callback_data="dp_zones"),
        )
        markup.add(
            types.InlineKeyboardButton("📈 Performance Report", callback_data="dp_performance"),
            types.InlineKeyboardButton("📦 Capacity Monitor", callback_data="dp_capacity"),
        )
        markup.add(
            types.InlineKeyboardButton("🔄 Refresh Data", callback_data="dp_refresh"),
            types.InlineKeyboardButton("⚠️ Capacity Alerts", callback_data="dp_alerts"),
        )

        admin_bot.send_message(
            message.chat.id,
            "🚚 **Delivery Personnel Management**\n\n"
            "Select an option to manage delivery personnel:",
            reply_markup=markup,
            parse_mode="Markdown"
        )

    except Exception as e:
        logger.error(f"Error in delivery_personnel_menu: {e}")
        admin_bot.reply_to(message, "❌ An error occurred. Please try again.")


@register_handler("admin", handler_type="callback_query", func=lambda call: call.data.startswith("dp_"))
def handle_delivery_personnel_callbacks(call):
    """Handle delivery personnel callback queries"""
    try:
        admin_bot.answer_callback_query(call.id)
        
        if call.data == "dp_view_all":
            view_all_personnel(call)
        elif call.data == "dp_register":
            start_personnel_registration(call)
        elif call.data == "dp_status":
            show_personnel_status(call)
        elif call.data == "dp_zones":
            manage_personnel_zones(call)
        elif call.data == "dp_performance":
            show_performance_report(call)
        elif call.data == "dp_refresh":
            refresh_personnel_data(call)
        elif call.data == "dp_capacity":
            show_capacity_monitor(call)
        elif call.data == "dp_alerts":
            show_capacity_alerts(call)
        elif call.data == "dp_validate_capacity":
            validate_capacity_data(call)
        elif call.data == "dp_availability":
            show_availability_status(call)
        elif call.data == "dp_validate_status":
            validate_status_data(call)
        elif call.data.startswith("dp_status_"):
            handle_status_action(call)
        elif call.data.startswith("dp_toggle_status_"):
            personnel_id = call.data.replace("dp_toggle_status_", "")
            toggle_personnel_status_handler(call, personnel_id)
        elif call.data.startswith("dp_edit_"):
            personnel_id = call.data.replace("dp_edit_", "")
            edit_personnel(call, personnel_id)
        elif call.data.startswith("dp_toggle_"):
            personnel_id = call.data.replace("dp_toggle_", "")
            toggle_personnel_status(call, personnel_id)
        elif call.data == "dp_menu":
            # Return to main delivery personnel menu
            show_delivery_personnel_menu(call)

    except Exception as e:
        logger.error(f"Error in handle_delivery_personnel_callbacks: {e}")
        admin_bot.send_message(call.message.chat.id, "❌ An error occurred. Please try again.")


def view_all_personnel(call):
    """Display all registered delivery personnel"""
    try:
        # Load fresh data
        personnel_data = load_delivery_personnel_data()
        
        if not personnel_data:
            admin_bot.edit_message_text(
                "📭 No delivery personnel registered yet.\n\n"
                "Use the 'Register New' option to add delivery personnel.",
                call.message.chat.id,
                call.message.message_id
            )
            return

        message_text = "👥 **Registered Delivery Personnel**\n\n"
        
        for personnel_id, data in personnel_data.items():
            personnel = DeliveryPersonnel.from_dict(data)
            status_emoji = {
                "available": "🟢",
                "busy": "🟡", 
                "offline": "🔴"
            }.get(personnel.status, "⚪")
            
            message_text += (
                f"{status_emoji} **{personnel.name}**\n"
                f"📱 {personnel.phone_number}\n"
                f"📍 Areas: {', '.join(personnel.service_areas) if personnel.service_areas else 'None'}\n"
                f"📦 Capacity: {personnel.current_capacity}/{personnel.max_capacity}\n"
                f"⭐ Rating: {personnel.rating:.1f} ({personnel.total_deliveries} deliveries)\n"
                f"✅ Verified: {'Yes' if personnel.is_verified else 'No'}\n\n"
            )

        # Add action buttons
        markup = types.InlineKeyboardMarkup(row_width=1)
        markup.add(types.InlineKeyboardButton("🔙 Back to Menu", callback_data="dp_back_menu"))
        
        admin_bot.edit_message_text(
            message_text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode="Markdown"
        )

    except Exception as e:
        logger.error(f"Error in view_all_personnel: {e}")
        admin_bot.send_message(call.message.chat.id, "❌ Error loading personnel data.")


def start_personnel_registration(call):
    """Start the personnel registration process"""
    try:
        user_id = call.from_user.id
        personnel_registration_data[user_id] = {
            "step": "name",
            "data": {}
        }
        
        admin_bot.edit_message_text(
            "👤 **Register New Delivery Personnel**\n\n"
            "Please enter the full name of the delivery person:",
            call.message.chat.id,
            call.message.message_id
        )
        
        # Register next step handler
        admin_bot.register_next_step_handler(
            call.message,
            process_personnel_name
        )

    except Exception as e:
        logger.error(f"Error in start_personnel_registration: {e}")
        admin_bot.send_message(call.message.chat.id, "❌ Error starting registration.")


def process_personnel_name(message):
    """Process the personnel name input"""
    try:
        user_id = message.from_user.id
        
        if user_id not in personnel_registration_data:
            admin_bot.reply_to(message, "❌ Registration session expired. Please start again.")
            return
            
        name = message.text.strip()
        if len(name) < 2:
            admin_bot.reply_to(message, "❌ Name must be at least 2 characters. Please try again:")
            admin_bot.register_next_step_handler(message, process_personnel_name)
            return
            
        personnel_registration_data[user_id]["data"]["name"] = name
        personnel_registration_data[user_id]["step"] = "phone"
        
        admin_bot.reply_to(
            message,
            f"✅ Name: {name}\n\n"
            "📱 Please enter the phone number (with country code, e.g., +251912345678):"
        )
        
        admin_bot.register_next_step_handler(message, process_personnel_phone)

    except Exception as e:
        logger.error(f"Error in process_personnel_name: {e}")
        admin_bot.reply_to(message, "❌ Error processing name. Please try again.")


def process_personnel_phone(message):
    """Process the personnel phone number input"""
    try:
        user_id = message.from_user.id
        
        if user_id not in personnel_registration_data:
            admin_bot.reply_to(message, "❌ Registration session expired. Please start again.")
            return
            
        phone = message.text.strip()
        if len(phone) < 10:
            admin_bot.reply_to(message, "❌ Invalid phone number. Please enter a valid phone number:")
            admin_bot.register_next_step_handler(message, process_personnel_phone)
            return
            
        personnel_registration_data[user_id]["data"]["phone_number"] = phone
        personnel_registration_data[user_id]["step"] = "areas"
        
        # Show available areas
        areas = areas_data.get("areas", [])
        if not areas:
            admin_bot.reply_to(message, "❌ No areas configured. Please configure areas first.")
            return
            
        markup = types.InlineKeyboardMarkup(row_width=2)
        for area in areas:
            markup.add(
                types.InlineKeyboardButton(
                    f"📍 {area['name']}", 
                    callback_data=f"dp_area_{area['id']}"
                )
            )
        markup.add(types.InlineKeyboardButton("✅ Finish Selection", callback_data="dp_areas_done"))
        
        admin_bot.reply_to(
            message,
            f"✅ Phone: {phone}\n\n"
            "🗺️ Please select the service areas (you can select multiple):",
            reply_markup=markup
        )

    except Exception as e:
        logger.error(f"Error in process_personnel_phone: {e}")
        admin_bot.reply_to(message, "❌ Error processing phone number. Please try again.")


def show_personnel_status(call):
    """Show current status of all delivery personnel"""
    try:
        # Load fresh data
        personnel_data = load_delivery_personnel_data()
        availability_data = load_delivery_personnel_availability_data()
        capacity_data = load_delivery_personnel_capacity_data()
        
        if not personnel_data:
            admin_bot.edit_message_text(
                "📭 No delivery personnel registered yet.",
                call.message.chat.id,
                call.message.message_id
            )
            return

        message_text = "📊 **Personnel Status Overview**\n\n"
        
        available_count = 0
        busy_count = 0
        offline_count = 0
        
        for personnel_id, data in personnel_data.items():
            personnel = DeliveryPersonnel.from_dict(data)
            current_capacity = capacity_data.get(personnel_id, 0)
            
            status_emoji = {
                "available": "🟢",
                "busy": "🟡", 
                "offline": "🔴"
            }.get(personnel.status, "⚪")
            
            if personnel.status == "available":
                available_count += 1
            elif personnel.status == "busy":
                busy_count += 1
            else:
                offline_count += 1
            
            message_text += (
                f"{status_emoji} **{personnel.name}**\n"
                f"📦 {current_capacity}/{personnel.max_capacity} orders\n"
                f"📍 {', '.join(personnel.service_areas[:2])}{'...' if len(personnel.service_areas) > 2 else ''}\n\n"
            )

        summary = (
            f"**Summary:**\n"
            f"🟢 Available: {available_count}\n"
            f"🟡 Busy: {busy_count}\n"
            f"🔴 Offline: {offline_count}\n\n"
        )
        
        message_text = summary + message_text
        
        markup = types.InlineKeyboardMarkup(row_width=1)
        markup.add(types.InlineKeyboardButton("🔄 Refresh", callback_data="dp_status"))
        markup.add(types.InlineKeyboardButton("🔙 Back to Menu", callback_data="dp_back_menu"))
        
        admin_bot.edit_message_text(
            message_text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode="Markdown"
        )

    except Exception as e:
        logger.error(f"Error in show_personnel_status: {e}")
        admin_bot.send_message(call.message.chat.id, "❌ Error loading status data.")


def manage_personnel_zones(call):
    """Manage personnel zone assignments"""
    admin_bot.edit_message_text(
        "🗺️ **Zone Management**\n\n"
        "Zone management features coming soon...\n"
        "This will allow you to:\n"
        "• Assign personnel to specific zones\n"
        "• View zone coverage\n"
        "• Optimize delivery routes",
        call.message.chat.id,
        call.message.message_id
    )


def show_performance_report(call):
    """Show performance report for all personnel"""
    admin_bot.edit_message_text(
        "📈 **Performance Report**\n\n"
        "Performance reporting features coming soon...\n"
        "This will show:\n"
        "• Delivery success rates\n"
        "• Average delivery times\n"
        "• Customer ratings\n"
        "• Monthly statistics",
        call.message.chat.id,
        call.message.message_id
    )


def refresh_personnel_data(call):
    """Refresh personnel data from Firebase"""
    try:
        # Reload all delivery personnel data
        global delivery_personnel, delivery_personnel_assignments
        global delivery_personnel_availability, delivery_personnel_capacity
        global delivery_personnel_zones, delivery_personnel_performance
        
        delivery_personnel.clear()
        delivery_personnel.update(load_delivery_personnel_data())
        
        delivery_personnel_assignments.clear()
        delivery_personnel_assignments.update(load_delivery_personnel_assignments_data())
        
        delivery_personnel_availability.clear()
        delivery_personnel_availability.update(load_delivery_personnel_availability_data())
        
        delivery_personnel_capacity.clear()
        delivery_personnel_capacity.update(load_delivery_personnel_capacity_data())
        
        delivery_personnel_zones.clear()
        delivery_personnel_zones.update(load_delivery_personnel_zones_data())
        
        delivery_personnel_performance.clear()
        delivery_personnel_performance.update(load_delivery_personnel_performance_data())
        
        admin_bot.edit_message_text(
            "✅ **Data Refreshed**\n\n"
            f"Loaded:\n"
            f"• {len(delivery_personnel)} personnel records\n"
            f"• {len(delivery_personnel_assignments)} assignments\n"
            f"• {len(delivery_personnel_availability)} availability records\n"
            f"• {len(delivery_personnel_capacity)} capacity records\n\n"
            "Data has been refreshed from Firebase.",
            call.message.chat.id,
            call.message.message_id
        )
        
    except Exception as e:
        logger.error(f"Error in refresh_personnel_data: {e}")
        admin_bot.edit_message_text(
            "❌ Error refreshing data. Please try again.",
            call.message.chat.id,
            call.message.message_id
        )


def show_capacity_monitor(call):
    """Show comprehensive capacity monitoring dashboard"""
    try:
        summary = get_capacity_summary()

        if not summary:
            admin_bot.edit_message_text(
                "❌ Error generating capacity summary.",
                call.message.chat.id,
                call.message.message_id
            )
            return

        message_text = "📦 **Capacity Monitoring Dashboard**\n\n"

        # Overall statistics
        message_text += (
            f"**📊 Overall Statistics:**\n"
            f"• Total Personnel: {summary['total_personnel']}\n"
            f"• Available: {summary['available_personnel']} 🟢\n"
            f"• Busy: {summary['busy_personnel']} 🟡\n"
            f"• Offline: {summary['offline_personnel']} 🔴\n\n"
            f"**📦 Capacity Overview:**\n"
            f"• Total Capacity: {summary['total_capacity']} orders\n"
            f"• Used Capacity: {summary['used_capacity']} orders\n"
            f"• Available Capacity: {summary['available_capacity']} orders\n"
            f"• Overall Utilization: {summary['overall_utilization']:.1f}%\n\n"
        )

        # Personnel details (top 10 by utilization)
        personnel_details = sorted(
            summary['personnel_details'],
            key=lambda x: x['utilization_rate'],
            reverse=True
        )[:10]

        message_text += "**👥 Personnel Capacity (Top 10):**\n"
        for person in personnel_details:
            status_emoji = {"available": "🟢", "busy": "🟡", "offline": "🔴"}.get(person['status'], "⚪")
            message_text += (
                f"{status_emoji} **{person['name']}**\n"
                f"   📦 {person['current_capacity']}/{person['max_capacity']} "
                f"({person['utilization_rate']:.0f}%)\n"
            )

        # Add action buttons
        markup = types.InlineKeyboardMarkup(row_width=2)
        markup.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="dp_capacity"),
            types.InlineKeyboardButton("⚠️ View Alerts", callback_data="dp_alerts"),
        )
        markup.add(
            types.InlineKeyboardButton("✅ Validate Data", callback_data="dp_validate_capacity"),
            types.InlineKeyboardButton("🔙 Back to Menu", callback_data="dp_menu"),
        )

        admin_bot.edit_message_text(
            message_text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode="Markdown"
        )

    except Exception as e:
        logger.error(f"Error in show_capacity_monitor: {e}")
        admin_bot.edit_message_text(
            "❌ Error showing capacity monitor.",
            call.message.chat.id,
            call.message.message_id
        )


def show_capacity_alerts(call):
    """Show capacity-related alerts and warnings"""
    try:
        alerts = monitor_capacity_alerts()

        message_text = "⚠️ **Capacity Alerts & Warnings**\n\n"

        if not alerts:
            message_text += "✅ **No alerts at this time**\n\nAll personnel are operating within normal capacity limits."
        else:
            high_capacity_alerts = [a for a in alerts if a['type'] == 'high_capacity']
            mismatch_alerts = [a for a in alerts if a['type'] == 'capacity_mismatch']

            if high_capacity_alerts:
                message_text += "🟡 **High Capacity Warnings:**\n"
                for alert in high_capacity_alerts:
                    message_text += f"• {alert['message']}\n"
                message_text += "\n"

            if mismatch_alerts:
                message_text += "🔴 **Capacity Data Mismatches:**\n"
                for alert in mismatch_alerts:
                    message_text += f"• {alert['message']}\n"
                message_text += "\n"

        # Add action buttons
        markup = types.InlineKeyboardMarkup(row_width=2)
        markup.add(
            types.InlineKeyboardButton("🔄 Refresh Alerts", callback_data="dp_alerts"),
            types.InlineKeyboardButton("📦 Capacity Monitor", callback_data="dp_capacity"),
        )
        markup.add(
            types.InlineKeyboardButton("✅ Validate & Fix", callback_data="dp_validate_capacity"),
            types.InlineKeyboardButton("🔙 Back to Menu", callback_data="dp_menu"),
        )

        admin_bot.edit_message_text(
            message_text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode="Markdown"
        )

    except Exception as e:
        logger.error(f"Error in show_capacity_alerts: {e}")
        admin_bot.edit_message_text(
            "❌ Error showing capacity alerts.",
            call.message.chat.id,
            call.message.message_id
        )


def validate_capacity_data(call):
    """Validate and sync all capacity data"""
    try:
        validation_results = validate_all_capacities()

        message_text = "✅ **Capacity Data Validation**\n\n"

        synced_count = sum(1 for result in validation_results.values() if result['synced'])
        total_count = len(validation_results)

        message_text += (
            f"**📊 Validation Summary:**\n"
            f"• Total Personnel: {total_count}\n"
            f"• Data Synced: {synced_count}\n"
            f"• Mismatches Fixed: {total_count - synced_count}\n\n"
        )

        if synced_count < total_count:
            message_text += "**🔧 Fixed Mismatches:**\n"
            for personnel_id, result in validation_results.items():
                if not result['synced']:
                    personnel_name = delivery_personnel.get(personnel_id, {}).get('name', personnel_id)
                    message_text += (
                        f"• {personnel_name}: {result['stored']} → {result['actual']}\n"
                    )
            message_text += "\n"

        message_text += "All capacity data has been validated and synchronized."

        # Add action buttons
        markup = types.InlineKeyboardMarkup(row_width=2)
        markup.add(
            types.InlineKeyboardButton("📦 Capacity Monitor", callback_data="dp_capacity"),
            types.InlineKeyboardButton("⚠️ View Alerts", callback_data="dp_alerts"),
        )
        markup.add(
            types.InlineKeyboardButton("🔙 Back to Menu", callback_data="dp_menu"),
        )

        admin_bot.edit_message_text(
            message_text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode="Markdown"
        )

    except Exception as e:
        logger.error(f"Error in validate_capacity_data: {e}")
        admin_bot.edit_message_text(
            "❌ Error validating capacity data.",
            call.message.chat.id,
            call.message.message_id
        )


def show_delivery_personnel_menu(call):
    """Show the main delivery personnel menu"""
    try:
        markup = types.InlineKeyboardMarkup(row_width=2)
        markup.add(
            types.InlineKeyboardButton("👥 View All Personnel", callback_data="dp_view_all"),
            types.InlineKeyboardButton("➕ Register New", callback_data="dp_register"),
        )
        markup.add(
            types.InlineKeyboardButton("📊 Personnel Status", callback_data="dp_status"),
            types.InlineKeyboardButton("🟢 Availability Status", callback_data="dp_availability"),
        )
        markup.add(
            types.InlineKeyboardButton("🗺️ Zone Management", callback_data="dp_zones"),
            types.InlineKeyboardButton("📈 Performance Report", callback_data="dp_performance"),
        )
        markup.add(
            types.InlineKeyboardButton("📦 Capacity Monitor", callback_data="dp_capacity"),
            types.InlineKeyboardButton("⚠️ Capacity Alerts", callback_data="dp_alerts"),
        )
        markup.add(
            types.InlineKeyboardButton("🔄 Refresh Data", callback_data="dp_refresh"),
            types.InlineKeyboardButton("✅ Validate Statuses", callback_data="dp_validate_status"),
        )

        admin_bot.edit_message_text(
            "🚚 **Delivery Personnel Management**\n\n"
            "Select an option to manage delivery personnel:",
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode="Markdown"
        )

    except Exception as e:
        logger.error(f"Error in show_delivery_personnel_menu: {e}")
        admin_bot.edit_message_text(
            "❌ Error showing menu.",
            call.message.chat.id,
            call.message.message_id
        )


# ===== AVAILABILITY STATUS MANAGEMENT HANDLERS =====

def show_availability_status(call):
    """Show comprehensive availability status dashboard"""
    try:
        status_summary = get_status_summary()

        # Build status message
        message = "🟢 **Availability Status Dashboard**\n\n"

        # Overall statistics
        message += f"📊 **Overall Statistics:**\n"
        message += f"• Total Personnel: {status_summary['total_personnel']}\n"
        message += f"• Available: {status_summary['status_counts']['available']} 🟢\n"
        message += f"• Busy: {status_summary['status_counts']['busy']} 🟡\n"
        message += f"• Offline: {status_summary['status_counts']['offline']} 🔴\n"
        message += f"• Verified: {status_summary['verified_counts']['verified']}\n"
        message += f"• Unverified: {status_summary['verified_counts']['unverified']}\n\n"

        # Available personnel details
        if status_summary['personnel_by_status']['available']:
            message += "🟢 **Available Personnel:**\n"
            for person in status_summary['personnel_by_status']['available']:
                capacity_info = f"{person['current_capacity']}/{person['max_capacity']}"
                verified_icon = "✅" if person['is_verified'] else "⚠️"
                message += f"• {person['name']} {verified_icon} ({capacity_info})\n"
            message += "\n"

        # Busy personnel details
        if status_summary['personnel_by_status']['busy']:
            message += "🟡 **Busy Personnel:**\n"
            for person in status_summary['personnel_by_status']['busy']:
                capacity_info = f"{person['current_capacity']}/{person['max_capacity']}"
                verified_icon = "✅" if person['is_verified'] else "⚠️"
                message += f"• {person['name']} {verified_icon} ({capacity_info})\n"
            message += "\n"

        # Offline personnel details
        if status_summary['personnel_by_status']['offline']:
            message += "🔴 **Offline Personnel:**\n"
            for person in status_summary['personnel_by_status']['offline']:
                verified_icon = "✅" if person['is_verified'] else "⚠️"
                message += f"• {person['name']} {verified_icon}\n"
            message += "\n"

        message += f"🕒 Last Updated: {status_summary['last_updated']}"

        # Create action buttons
        markup = types.InlineKeyboardMarkup(row_width=2)
        markup.add(
            types.InlineKeyboardButton("🟢 View Available", callback_data="dp_status_available"),
            types.InlineKeyboardButton("🟡 View Busy", callback_data="dp_status_busy"),
        )
        markup.add(
            types.InlineKeyboardButton("🔴 View Offline", callback_data="dp_status_offline"),
            types.InlineKeyboardButton("🔄 Refresh Status", callback_data="dp_availability"),
        )
        markup.add(
            types.InlineKeyboardButton("✅ Validate All", callback_data="dp_validate_status"),
            types.InlineKeyboardButton("🔙 Back to Menu", callback_data="dp_menu"),
        )

        admin_bot.edit_message_text(
            message,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode="Markdown"
        )

    except Exception as e:
        logger.error(f"Error in show_availability_status: {e}")
        admin_bot.send_message(call.message.chat.id, "❌ Error displaying availability status.")


def handle_status_action(call):
    """Handle status-specific actions (view by status)"""
    try:
        status = call.data.replace("dp_status_", "")
        personnel_list = get_personnel_by_status(status)

        status_icons = {"available": "🟢", "busy": "🟡", "offline": "🔴"}
        icon = status_icons.get(status, "📋")

        message = f"{icon} **{status.title()} Personnel**\n\n"

        if not personnel_list:
            message += f"No personnel currently {status}.\n"
        else:
            message += f"Found {len(personnel_list)} {status} personnel:\n\n"

            for person in personnel_list:
                verified_icon = "✅" if person['is_verified'] else "⚠️"
                capacity_info = f"{person['current_capacity']}/{person['max_capacity']}"
                areas = ", ".join(person['service_areas'][:2])  # Show first 2 areas
                if len(person['service_areas']) > 2:
                    areas += f" (+{len(person['service_areas'])-2} more)"

                message += f"👤 **{person['name']}** {verified_icon}\n"
                message += f"📱 {person['phone_number']}\n"
                message += f"📦 Capacity: {capacity_info}\n"
                message += f"🗺️ Areas: {areas}\n"
                message += f"🆔 ID: `{person['personnel_id']}`\n\n"

        # Create action buttons
        markup = types.InlineKeyboardMarkup(row_width=2)

        if personnel_list:
            # Add toggle buttons for each person
            for person in personnel_list[:5]:  # Limit to first 5 to avoid button overflow
                button_text = f"🔄 Toggle {person['name'][:10]}"
                callback_data = f"dp_toggle_status_{person['personnel_id']}"
                markup.add(types.InlineKeyboardButton(button_text, callback_data=callback_data))

        markup.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data=f"dp_status_{status}"),
            types.InlineKeyboardButton("🔙 Back", callback_data="dp_availability"),
        )

        admin_bot.edit_message_text(
            message,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode="Markdown"
        )

    except Exception as e:
        logger.error(f"Error in handle_status_action: {e}")
        admin_bot.send_message(call.message.chat.id, "❌ Error handling status action.")


def toggle_personnel_status_handler(call, personnel_id):
    """Handle toggling personnel status"""
    try:
        # Get current status
        current_status = get_personnel_status(personnel_id)
        if current_status is None:
            admin_bot.answer_callback_query(call.id, "❌ Personnel not found!")
            return

        # Toggle status
        new_status = toggle_personnel_status(personnel_id)
        if new_status is None:
            admin_bot.answer_callback_query(call.id, "❌ Failed to toggle status!")
            return

        # Get personnel name for feedback
        personnel_data = delivery_personnel.get(personnel_id, {})
        personnel_name = personnel_data.get('name', 'Unknown')

        # Provide feedback
        status_icons = {"available": "🟢", "busy": "🟡", "offline": "🔴"}
        old_icon = status_icons.get(current_status, "❓")
        new_icon = status_icons.get(new_status, "❓")

        feedback_message = f"{personnel_name}: {old_icon} → {new_icon}"
        admin_bot.answer_callback_query(call.id, feedback_message)

        # Refresh the current view
        show_availability_status(call)

    except Exception as e:
        logger.error(f"Error in toggle_personnel_status_handler: {e}")
        admin_bot.answer_callback_query(call.id, "❌ Error toggling status!")


def validate_status_data(call):
    """Validate and synchronize all personnel statuses"""
    try:
        admin_bot.answer_callback_query(call.id, "🔄 Validating statuses...")

        validation_results = validate_all_statuses()

        message = "✅ **Status Validation Complete**\n\n"
        message += f"📊 **Validation Summary:**\n"
        message += f"• Total Checked: {validation_results['total_checked']}\n"
        message += f"• Corrections Made: {validation_results['corrections_made']}\n"
        message += f"• Mismatches Found: {len(validation_results['status_mismatches'])}\n\n"

        if validation_results['corrected_personnel']:
            message += "🔧 **Corrections Made:**\n"
            for correction in validation_results['corrected_personnel']:
                old_icon = {"available": "🟢", "busy": "🟡", "offline": "🔴"}.get(correction['old_status'], "❓")
                new_icon = {"available": "🟢", "busy": "🟡", "offline": "🔴"}.get(correction['new_status'], "❓")
                message += f"• {correction['name']}: {old_icon} → {new_icon}\n"
            message += "\n"

        if validation_results['status_mismatches'] and not validation_results['corrected_personnel']:
            message += "⚠️ **Issues Found (Not Auto-Corrected):**\n"
            for mismatch in validation_results['status_mismatches'][:3]:  # Show first 3
                message += f"• {mismatch['name']}: Status mismatch detected\n"
            if len(validation_results['status_mismatches']) > 3:
                message += f"• ... and {len(validation_results['status_mismatches'])-3} more\n"
            message += "\n"

        if validation_results['corrections_made'] == 0 and len(validation_results['status_mismatches']) == 0:
            message += "✅ All statuses are correctly synchronized!\n\n"

        message += f"🕒 Validation Time: {validation_results['validation_time']}"

        # Create action buttons
        markup = types.InlineKeyboardMarkup(row_width=2)
        markup.add(
            types.InlineKeyboardButton("🔄 Run Again", callback_data="dp_validate_status"),
            types.InlineKeyboardButton("📊 View Status", callback_data="dp_availability"),
        )
        markup.add(
            types.InlineKeyboardButton("🔙 Back to Menu", callback_data="dp_menu"),
        )

        admin_bot.edit_message_text(
            message,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode="Markdown"
        )

    except Exception as e:
        logger.error(f"Error in validate_status_data: {e}")
        admin_bot.send_message(call.message.chat.id, "❌ Error validating status data.")
