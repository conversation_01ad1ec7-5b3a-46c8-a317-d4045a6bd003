# ===============================================================================
# Wiz Aroma Food Delivery System v2.0 - Environment Configuration
# ===============================================================================
# This file contains all environment variables required for the system to function.
# Copy this file to .env and replace all placeholder values with your actual data.
#
# SECURITY NOTE: Never commit the actual .env file to version control!
# ===============================================================================

# ===============================================================================
# TELEGRAM BOT TOKENS (REQUIRED)
# ===============================================================================
# All bot tokens are required for the multi-bot system to function properly.
# Get these tokens from @BotFather on Telegram.

# Main customer-facing bot for ordering
BOT_TOKEN=your_user_bot_token_here

# Administrative bot for order management and oversight
ADMIN_BOT_TOKEN=your_admin_bot_token_here

# Finance bot for payment verification (legacy - being phased out)
FINANCE_BOT_TOKEN=your_finance_bot_token_here

# Maintenance bot for system configuration
MAINTENANCE_BOT_TOKEN=your_maintenance_bot_token_here

# Management bot for analytics, personnel management, and reporting
MANAGEMENT_BOT_TOKEN=your_management_bot_token_here

# Order tracking bot for internal order monitoring
ORDER_TRACK_BOT_TOKEN=your_order_track_bot_token_here

# Delivery bot for broadcast-based order assignment to delivery personnel
DELIVERY_BOT_TOKEN=your_delivery_bot_token_here

# ===============================================================================
# AUTHORIZATION & ACCESS CONTROL (REQUIRED)
# ===============================================================================
# These IDs control who can access different bot functions.
# Use actual Telegram user IDs (numbers, not usernames).

# Primary system administrator (REQUIRED - used as fallback for all bots)
SYSTEM_ADMIN_ID=your_primary_admin_telegram_id_here

# Admin chat IDs for general administrative functions (JSON array format)
ADMIN_CHAT_IDS="[your_admin_id_here]"

# Authorized users for order tracking bot (JSON array format)
ORDER_TRACK_BOT_AUTHORIZED_IDS="[your_admin_id_here]"

# Authorized users for delivery bot (JSON array format)
# Note: Delivery personnel are managed dynamically via Firebase
DELIVERY_BOT_AUTHORIZED_IDS="[your_admin_id_here]"

# Authorized users for management bot (JSON array format)
MANAGEMENT_BOT_AUTHORIZED_IDS="[your_admin_id_here, secondary_admin_id_here]"

# ===============================================================================
# LEGACY CHAT IDs (OPTIONAL - FOR BACKWARD COMPATIBILITY)
# ===============================================================================
# These are maintained for compatibility with older bot functions.

FINANCE_CHAT_ID=your_finance_id_here
MAINTENANCE_CHAT_ID=your_maintenance_id_here
MANAGEMENT_CHAT_ID=your_management_id_here

# ===============================================================================
# FIREBASE CONFIGURATION (REQUIRED)
# ===============================================================================
# Firebase is the exclusive data storage system for Wiz Aroma v2.0.
# No local fallbacks are used - all data is stored and accessed from Firebase.

# Firebase Realtime Database URL (REQUIRED)
FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com/

# Firebase Service Account Credentials (CHOOSE ONE METHOD):

# Method 1: JSON credentials as environment variable (RECOMMENDED FOR PRODUCTION)
# Paste your entire Firebase service account JSON as a single line:
# FIREBASE_CREDENTIALS={"type":"service_account","project_id":"your-project-id","private_key_id":"your-private-key-id","private_key":"-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY\n-----END PRIVATE KEY-----\n","client_email":"*******","client_id":"your-client-id","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xxxxx%40your-project-id.iam.gserviceaccount.com"}

# Method 2: Path to Firebase credentials JSON file (RECOMMENDED FOR DEVELOPMENT)
# Place your Firebase service account JSON file in the project root and specify the filename:
FIREBASE_CREDENTIALS_PATH=wiz-aroma-firebase-adminsdk.json

# ===============================================================================
# PAYMENT INFORMATION (REQUIRED)
# ===============================================================================
# Payment account details displayed to customers during checkout.
# These are shown in payment instructions within the bot.

# Telebirr Mobile Payment
TELEBIRR_PHONE=**********
TELEBIRR_NAME=Your Business Name

# Commercial Bank of Ethiopia (CBE)
CBE_ACCOUNT_NUMBER=****************
CBE_ACCOUNT_NAME=Your Business Name

# Bank of Abyssinia (BOA)
BOA_ACCOUNT_NUMBER=****************
BOA_ACCOUNT_NAME=Your Business Name

# ===============================================================================
# CONTACT & SUPPORT INFORMATION (REQUIRED)
# ===============================================================================
# Contact details displayed to customers for support and inquiries.

# Primary support phone numbers
SUPPORT_PHONE_1=+************
SUPPORT_PHONE_2=+************

# Telegram support handle (without @)
SUPPORT_TELEGRAM=your_support_handle

# Customer service contact information
CUSTOMER_SERVICE_PHONE=+************
CUSTOMER_SERVICE_EMAIL=*******
BUSINESS_EMAIL=*******

# ===============================================================================
# EMAIL CONFIGURATION (OPTIONAL)
# ===============================================================================
# Email settings for order notifications and system alerts.
# Currently used for order completion notifications.

EMAIL_ADDRESS=*******
EMAIL_PASSWORD=your_app_specific_password_here

# ===============================================================================
# APPLICATION CONFIGURATION (OPTIONAL)
# ===============================================================================
# System behavior and logging configuration.

# Test mode - set to False for production, True for development/testing
TEST_MODE=False

# Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO