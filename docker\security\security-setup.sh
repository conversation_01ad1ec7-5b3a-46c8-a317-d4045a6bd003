#!/bin/bash
# Wiz-Aroma Docker Security Setup Script
# This script implements security best practices for Docker deployment

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
    fi
}

# Check Docker installation and version
check_docker() {
    log "Checking Docker installation..."
    
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check Docker version
    DOCKER_VERSION=$(docker --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
    log "Docker version: $DOCKER_VERSION"
    
    # Check if user is in docker group
    if ! groups | grep -q docker; then
        warn "User is not in docker group. You may need to run 'sudo usermod -aG docker $USER' and restart your session."
    fi
}

# Create secure directories
create_directories() {
    log "Creating secure directory structure..."
    
    # Create directories with proper permissions
    mkdir -p logs data_files docker/nginx/ssl docker/secrets
    
    # Set secure permissions
    chmod 750 logs data_files
    chmod 700 docker/secrets
    chmod 755 docker/nginx/ssl
    
    log "Directory structure created with secure permissions"
}

# Generate secure passwords
generate_passwords() {
    log "Generating secure passwords..."
    
    # Generate Redis password if not exists
    if [[ ! -f docker/secrets/redis_password ]]; then
        openssl rand -base64 32 > docker/secrets/redis_password
        chmod 600 docker/secrets/redis_password
        log "Redis password generated"
    fi
    
    # Generate Grafana password if not exists
    if [[ ! -f docker/secrets/grafana_password ]]; then
        openssl rand -base64 32 > docker/secrets/grafana_password
        chmod 600 docker/secrets/grafana_password
        log "Grafana password generated"
    fi
}

# Setup SSL certificates (self-signed for development)
setup_ssl() {
    log "Setting up SSL certificates..."
    
    SSL_DIR="docker/nginx/ssl"
    
    if [[ ! -f "$SSL_DIR/cert.pem" ]] || [[ ! -f "$SSL_DIR/key.pem" ]]; then
        # Generate self-signed certificate for development
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout "$SSL_DIR/key.pem" \
            -out "$SSL_DIR/cert.pem" \
            -subj "/C=ET/ST=Addis Ababa/L=Addis Ababa/O=Wiz-Aroma/CN=localhost"
        
        chmod 600 "$SSL_DIR/key.pem"
        chmod 644 "$SSL_DIR/cert.pem"
        
        log "Self-signed SSL certificate generated for development"
        warn "For production, replace with proper SSL certificates from a trusted CA"
    fi
}

# Validate environment file
validate_env() {
    log "Validating environment configuration..."
    
    if [[ ! -f .env ]]; then
        if [[ -f .env.docker ]]; then
            log "Copying .env.docker to .env"
            cp .env.docker .env
        else
            error ".env file not found. Please create one based on .env.docker template"
        fi
    fi
    
    # Check for required variables
    REQUIRED_VARS=(
        "BOT_TOKEN"
        "ADMIN_BOT_TOKEN"
        "FIREBASE_DATABASE_URL"
        "SYSTEM_ADMIN_ID"
        "FIREBASE_CREDENTIALS_FILE"
    )
    
    for var in "${REQUIRED_VARS[@]}"; do
        if ! grep -q "^${var}=" .env || grep -q "^${var}=$" .env || grep -q "^${var}=.*_here" .env; then
            error "Required environment variable $var is not properly set in .env file"
        fi
    done
    
    log "Environment configuration validated"
}

# Check Firebase credentials
check_firebase() {
    log "Checking Firebase credentials..."
    
    FIREBASE_CREDS_FILE=$(grep "^FIREBASE_CREDENTIALS_FILE=" .env | cut -d'=' -f2)
    
    if [[ ! -f "$FIREBASE_CREDS_FILE" ]]; then
        error "Firebase credentials file not found: $FIREBASE_CREDS_FILE"
    fi
    
    # Validate JSON format
    if ! python3 -m json.tool "$FIREBASE_CREDS_FILE" > /dev/null 2>&1; then
        error "Firebase credentials file is not valid JSON"
    fi
    
    # Set secure permissions
    chmod 600 "$FIREBASE_CREDS_FILE"
    
    log "Firebase credentials validated"
}

# Setup Docker security
setup_docker_security() {
    log "Configuring Docker security settings..."
    
    # Create Docker daemon configuration if it doesn't exist
    DOCKER_DAEMON_CONFIG="/etc/docker/daemon.json"
    
    if [[ ! -f "$DOCKER_DAEMON_CONFIG" ]] && [[ -w "/etc/docker" ]]; then
        cat > "$DOCKER_DAEMON_CONFIG" << EOF
{
    "live-restore": true,
    "userland-proxy": false,
    "no-new-privileges": true,
    "seccomp-profile": "/etc/docker/seccomp.json",
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "10m",
        "max-file": "3"
    }
}
EOF
        log "Docker daemon configuration created"
        warn "Please restart Docker daemon to apply security settings"
    fi
}

# Main execution
main() {
    log "Starting Wiz-Aroma Docker Security Setup..."
    
    check_root
    check_docker
    create_directories
    generate_passwords
    setup_ssl
    validate_env
    check_firebase
    setup_docker_security
    
    log "Security setup completed successfully!"
    log "Next steps:"
    log "1. Review and customize .env file with your actual credentials"
    log "2. Run: docker-compose up -d"
    log "3. Check logs: docker-compose logs -f"
    log "4. Access monitoring at: http://localhost:3000 (Grafana)"
}

# Run main function
main "$@"
