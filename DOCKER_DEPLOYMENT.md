# 🐳 Wiz-Aroma Docker Deployment Guide

## 📋 Overview

This guide provides comprehensive instructions for deploying the Wiz-Aroma Food Delivery System v2.0 using Docker containers. The containerized setup includes all bot services, monitoring, security hardening, and production-ready configurations.

## 🎯 What's Included

### Core Services

- **Main Application**: Multi-bot Telegram system with Firebase integration
- **Redis**: Caching and session management
- **Nginx**: Reverse proxy and load balancer

### Monitoring Stack (Optional)

- **Prometheus**: Metrics collection
- **Grafana**: Visualization dashboard
- **Loki**: Log aggregation
- **AlertManager**: Alert management

### Security Features

- Non-root user execution
- Read-only containers
- Network isolation
- Secrets management
- SSL/TLS encryption

## 🔧 Prerequisites

### System Requirements

- **OS**: Linux, macOS, or Windows with WSL2
- **Docker**: Version 20.10+
- **Docker Compose**: Version 2.0+
- **Memory**: Minimum 2GB RAM (4GB recommended)
- **Storage**: 10GB free space

### Required Credentials

- 7 Telegram bot tokens (from @BotFather)
- Firebase project with service account credentials
- Payment account information
- System administrator Telegram ID

## 🚀 Quick Start

### 1. <PERSON><PERSON> and Setup

```bash
# Clone the repository
git clone https://github.com/your-repo/Wiz-Aroma-V-1.3.3.git
cd Wiz-Aroma-V-1.3.3

# Make security setup script executable
chmod +x docker/security/security-setup.sh

# Run security setup (Linux/macOS)
./docker/security/security-setup.sh
```

### 2. Configure Environment

```bash
# Copy environment template
cp .env.docker .env

# Edit with your credentials
nano .env  # or use your preferred editor
```

### 3. Add Firebase Credentials

```bash
# Place your Firebase service account JSON file in the project root
# Example: wiz-aroma-firebase-adminsdk.json
```

### 4. Deploy Application

```bash
# Build and start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f wiz-aroma-app
```

## 📝 Detailed Configuration

### Environment Variables

#### Required Bot Tokens

```env
BOT_TOKEN=your_main_bot_token
ADMIN_BOT_TOKEN=your_admin_bot_token
FINANCE_BOT_TOKEN=your_finance_bot_token
MAINTENANCE_BOT_TOKEN=your_maintenance_bot_token
MANAGEMENT_BOT_TOKEN=your_management_bot_token
ORDER_TRACK_BOT_TOKEN=your_order_track_bot_token
DELIVERY_BOT_TOKEN=your_delivery_bot_token
```

#### Firebase Configuration

```env
FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com/
FIREBASE_CREDENTIALS_FILE=./wiz-aroma-firebase-adminsdk.json
```

#### System Authorization

```env
SYSTEM_ADMIN_ID=your_telegram_user_id
ORDER_TRACK_BOT_AUTHORIZED_IDS=[user_id_1, user_id_2]
DELIVERY_BOT_AUTHORIZED_IDS=[user_id_1, user_id_2]
MANAGEMENT_BOT_AUTHORIZED_IDS=[user_id_1]
```

### Firebase Setup

1. **Create Firebase Project**
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Create new project or use existing
   - Enable Firestore and Realtime Database

2. **Generate Service Account**
   - Go to Project Settings → Service Accounts
   - Generate new private key
   - Download JSON file
   - Place in project root

3. **Configure Security Rules**
   - Set appropriate Firestore security rules
   - Configure Realtime Database rules

## 🔍 Monitoring and Observability

### Enable Monitoring Stack

```bash
# Start with monitoring services
docker-compose -f docker-compose.yml -f docker/monitoring/docker-compose.monitoring.yml up -d
```

### Access Dashboards

- **Grafana**: <http://localhost:3000> (admin/admin123)
- **Prometheus**: <http://localhost:9090>
- **AlertManager**: <http://localhost:9093>

### View Logs

```bash
# Application logs
docker-compose logs -f wiz-aroma-app

# All services logs
docker-compose logs -f

# Specific service logs
docker-compose logs -f redis nginx
```

## 🛡️ Security Hardening

### Production Security

```bash
# Use security-enhanced compose file
docker-compose -f docker-compose.yml -f docker/security/docker-compose.security.yml up -d
```

### Security Checklist

- [ ] Change all default passwords
- [ ] Use strong, unique credentials
- [ ] Enable SSL/TLS in production
- [ ] Configure firewall rules
- [ ] Regular security updates
- [ ] Monitor access logs

## 🔧 Management Commands

### Service Management

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# Restart specific service
docker-compose restart wiz-aroma-app

# Scale services (if needed)
docker-compose up -d --scale wiz-aroma-app=2
```

### Data Management

```bash
# Backup volumes
docker run --rm -v wiz-aroma-data:/data -v $(pwd):/backup alpine tar czf /backup/data-backup.tar.gz /data

# Restore volumes
docker run --rm -v wiz-aroma-data:/data -v $(pwd):/backup alpine tar xzf /backup/data-backup.tar.gz -C /
```

### Health Checks

```bash
# Check container health
docker-compose ps

# Detailed health status
docker inspect --format='{{.State.Health.Status}}' wiz-aroma-main

# Manual health check
docker exec wiz-aroma-main python /app/health_check.py
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Container Won't Start

```bash
# Check logs for errors
docker-compose logs wiz-aroma-app

# Common causes:
# - Missing environment variables
# - Invalid Firebase credentials
# - Port conflicts
# - Insufficient permissions
```

#### 2. Firebase Connection Issues

```bash
# Verify credentials file exists and is valid
ls -la wiz-aroma-firebase-*.json
python3 -m json.tool wiz-aroma-firebase-*.json

# Check Firebase URL format
grep FIREBASE_DATABASE_URL .env
```

#### 3. Bot Token Issues

```bash
# Test bot tokens manually
curl -X GET "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getMe"

# Check environment variables
docker exec wiz-aroma-main env | grep BOT_TOKEN
```

#### 4. Memory/Resource Issues

```bash
# Check resource usage
docker stats

# Increase memory limits in docker-compose.yml
# deploy:
#   resources:
#     limits:
#       memory: 1G
```

#### 5. Network Connectivity

```bash
# Test container networking
docker exec wiz-aroma-main ping google.com

# Check port bindings
docker port wiz-aroma-main
```

### Debug Mode

```bash
# Run with debug logging
docker-compose down
docker-compose up -d --build
docker-compose logs -f wiz-aroma-app
```

### Performance Optimization

```bash
# Monitor resource usage
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

# Optimize for production
# - Enable Redis caching
# - Use production logging levels
# - Configure resource limits
```

## 🔄 Updates and Maintenance

### Application Updates

```bash
# Pull latest changes
git pull origin main

# Rebuild and restart
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### Database Maintenance

```bash
# Firebase data cleanup (if needed)
# Use management bot commands or Firebase console

# Local data cleanup
docker exec wiz-aroma-main python main.py --initialize-data
```

### Log Rotation

```bash
# Configure log rotation in docker-compose.yml
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

## 🌐 Production Deployment

### SSL/HTTPS Setup

```bash
# 1. Obtain SSL certificates (Let's Encrypt recommended)
# 2. Place certificates in docker/nginx/ssl/
# 3. Update nginx configuration
# 4. Enable HTTPS in docker-compose.yml
```

### Domain Configuration

```bash
# Update nginx.conf with your domain
server_name your-domain.com www.your-domain.com;

# Configure DNS records
# A record: your-domain.com → your-server-ip
# CNAME: www.your-domain.com → your-domain.com
```

### Backup Strategy

```bash
# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker run --rm -v wiz-aroma-data:/data -v /backup:/backup alpine \
  tar czf /backup/wiz-aroma-backup-$DATE.tar.gz /data
```

## 📊 Monitoring Setup

### Grafana Dashboards

1. Import pre-built dashboards for Docker monitoring
2. Create custom dashboards for business metrics
3. Set up alerting rules

### Log Analysis

```bash
# Search logs with specific patterns
docker-compose logs | grep ERROR
docker-compose logs | grep "order.*failed"

# Export logs for analysis
docker-compose logs > application.log
```

## 🔐 Security Best Practices

### Regular Security Tasks

- [ ] Update base images monthly
- [ ] Rotate passwords quarterly
- [ ] Review access logs weekly
- [ ] Update dependencies regularly
- [ ] Backup encryption keys

### Security Monitoring

```bash
# Monitor failed login attempts
docker-compose logs | grep "unauthorized"

# Check for suspicious activity
docker-compose logs | grep -E "(failed|error|unauthorized|denied)"
```

## 📞 Support and Resources

### Getting Help

- **Documentation**: Check project README and documentation
- **Logs**: Always include relevant logs when reporting issues
- **Environment**: Provide system information and configuration details

### Useful Commands Reference

```bash
# Quick status check
docker-compose ps && docker-compose logs --tail=50

# Complete restart
docker-compose down && docker-compose up -d

# Emergency stop
docker-compose kill

# Clean restart (removes volumes - USE WITH CAUTION)
docker-compose down -v && docker-compose up -d
```

---

## 📋 Deployment Checklist

### Pre-Deployment

- [ ] All bot tokens configured
- [ ] Firebase credentials in place
- [ ] Environment variables set
- [ ] SSL certificates ready (production)
- [ ] Backup strategy implemented

### Post-Deployment

- [ ] All services running
- [ ] Health checks passing
- [ ] Monitoring configured
- [ ] Logs flowing correctly
- [ ] Bot functionality tested

### Production Readiness

- [ ] Security hardening applied
- [ ] Resource limits configured
- [ ] Backup automation setup
- [ ] Monitoring alerts configured
- [ ] Documentation updated
