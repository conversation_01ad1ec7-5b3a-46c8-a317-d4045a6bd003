# Minimal Docker Compose for Wiz-Aroma - Fast Deployment

services:
  wiz-aroma-app:
    build:
      context: .
      dockerfile: Dockerfile.minimal
    container_name: wiz-aroma-main
    restart: unless-stopped
    environment:
      # Bot Tokens
      - BOT_TOKEN=${BOT_TOKEN}
      - ADMIN_BOT_TOKEN=${ADMIN_BOT_TOKEN}
      - FINANCE_BOT_TOKEN=${FINANCE_BOT_TOKEN}
      - MAINTENANCE_BOT_TOKEN=${MAINTENANCE_BOT_TOKEN}
      - MANAGEMENT_BOT_TOKEN=${MANAGEMENT_BOT_TOKEN}
      - ORDER_TRACK_BOT_TOKEN=${ORDER_TRACK_BOT_TOKEN}
      - DELIVERY_BOT_TOKEN=${DELIVERY_BOT_TOKEN}
      
      # Firebase Configuration
      - FIREBASE_DATABASE_URL=${FIREBASE_DATABASE_URL}
      - FIREBASE_CREDENTIALS_PATH=/app/firebase-credentials.json
      
      # System Configuration
      - SYSTEM_ADMIN_ID=${SYSTEM_ADMIN_ID}
      - ORDER_TRACK_BOT_AUTHORIZED_IDS=${ORDER_TRACK_BOT_AUTHORIZED_IDS}
      - DELIVERY_BOT_AUTHORIZED_IDS=${DELIVERY_BOT_AUTHORIZED_IDS}
      - MANAGEMENT_BOT_AUTHORIZED_IDS=${MANAGEMENT_BOT_AUTHORIZED_IDS}
      
      # Payment Information
      - TELEBIRR_PHONE=${TELEBIRR_PHONE}
      - TELEBIRR_NAME=${TELEBIRR_NAME}
      - CBE_ACCOUNT_NUMBER=${CBE_ACCOUNT_NUMBER}
      - CBE_ACCOUNT_NAME=${CBE_ACCOUNT_NAME}
      - BOA_ACCOUNT_NUMBER=${BOA_ACCOUNT_NUMBER}
      - BOA_ACCOUNT_NAME=${BOA_ACCOUNT_NAME}
      
      # Contact Information
      - SUPPORT_PHONE_1=${SUPPORT_PHONE_1}
      - SUPPORT_PHONE_2=${SUPPORT_PHONE_2}
      - SUPPORT_TELEGRAM=${SUPPORT_TELEGRAM}
      - CUSTOMER_SERVICE_PHONE=${CUSTOMER_SERVICE_PHONE}
      - CUSTOMER_SERVICE_EMAIL=${CUSTOMER_SERVICE_EMAIL}
      - BUSINESS_EMAIL=${BUSINESS_EMAIL}
      
      # Application Settings
      - TEST_MODE=${TEST_MODE:-false}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - PYTHONPATH=/app
    
    volumes:
      # Persistent data storage
      - ./logs:/app/logs
      - ./data_files:/app/data_files
      # Firebase credentials
      - ${FIREBASE_CREDENTIALS_FILE}:/app/firebase-credentials.json:ro
    
    ports:
      - "8080:8080"
    
    healthcheck:
      test: ["CMD", "python", "/app/health_check.py"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
