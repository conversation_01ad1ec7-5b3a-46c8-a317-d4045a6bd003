# 🐳 Wiz-Aroma Docker Setup

## Quick Start

### 1. One-Command Setup
```bash
# Make deployment script executable and run setup
chmod +x deploy.sh
./deploy.sh setup
```

### 2. Configure Environment
```bash
# Edit the .env file with your credentials
nano .env
```

### 3. Deploy
```bash
# Start all services
./deploy.sh start

# Or start with monitoring
./deploy.sh monitor

# Or start with security hardening
./deploy.sh security
```

## 📁 Docker Files Structure

```
├── Dockerfile                              # Main application container
├── docker-compose.yml                      # Core services orchestration
├── .dockerignore                           # Docker build exclusions
├── .env.docker                            # Environment template
├── deploy.sh                              # Deployment automation script
└── docker/
    ├── nginx/
    │   └── nginx.conf                      # Reverse proxy configuration
    ├── monitoring/
    │   ├── docker-compose.monitoring.yml   # Monitoring stack
    │   ├── prometheus.yml                  # Metrics configuration
    │   └── alert_rules.yml                 # Alerting rules
    └── security/
        ├── docker-compose.security.yml     # Security hardening
        └── security-setup.sh               # Security automation
```

## 🚀 Deployment Options

### Basic Deployment
```bash
./deploy.sh start
```
- Main application with <PERSON><PERSON> and Nginx
- Basic health checks and logging
- Suitable for development and testing

### With Monitoring
```bash
./deploy.sh monitor
```
- Includes Prometheus, Grafana, Loki
- Comprehensive metrics and logging
- Alerting and visualization dashboards
- Access Grafana at http://localhost:3000

### With Security Hardening
```bash
./deploy.sh security
```
- Enhanced container security
- Read-only filesystems
- Dropped capabilities
- Network isolation

### Production Deployment
```bash
# Combine monitoring and security
docker-compose \
  -f docker-compose.yml \
  -f docker/monitoring/docker-compose.monitoring.yml \
  -f docker/security/docker-compose.security.yml \
  up -d
```

## 🔧 Management Commands

### Service Management
```bash
./deploy.sh start      # Start all services
./deploy.sh stop       # Stop all services
./deploy.sh restart    # Restart all services
./deploy.sh status     # Show service status
./deploy.sh logs       # Show logs
./deploy.sh logs -f    # Follow logs
```

### Maintenance
```bash
./deploy.sh update     # Update and restart services
./deploy.sh backup     # Create data backup
./deploy.sh clean      # Clean up (DESTRUCTIVE)
```

## 📊 Monitoring Access

When deployed with monitoring stack:

- **Grafana**: http://localhost:3000
  - Username: admin
  - Password: admin123 (change in production)
- **Prometheus**: http://localhost:9090
- **AlertManager**: http://localhost:9093

## 🔐 Security Features

### Container Security
- Non-root user execution
- Read-only root filesystems
- Dropped Linux capabilities
- Security options enabled
- Resource limits enforced

### Network Security
- Isolated Docker networks
- Internal service communication
- Nginx reverse proxy
- SSL/TLS ready configuration

### Secrets Management
- Environment variable isolation
- Secure credential handling
- Firebase credentials protection
- Password generation automation

## 🐛 Troubleshooting

### Quick Diagnostics
```bash
# Check service status
./deploy.sh status

# View recent logs
./deploy.sh logs

# Check container health
docker inspect --format='{{.State.Health.Status}}' wiz-aroma-main
```

### Common Issues

#### Services Won't Start
```bash
# Check logs for errors
docker-compose logs wiz-aroma-app

# Verify environment configuration
grep -E "(TOKEN|FIREBASE)" .env
```

#### Firebase Connection Issues
```bash
# Verify credentials file
ls -la wiz-aroma-firebase-*.json
python3 -m json.tool wiz-aroma-firebase-*.json
```

#### Resource Issues
```bash
# Check resource usage
docker stats

# Free up space
docker system prune -f
```

## 🔄 Updates and Maintenance

### Application Updates
```bash
# Automated update
./deploy.sh update

# Manual update
git pull origin main
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### Data Backup
```bash
# Create backup
./deploy.sh backup

# Manual backup
docker run --rm \
  -v wiz-aroma-data:/data \
  -v $(pwd)/backups:/backup \
  alpine tar czf /backup/backup-$(date +%Y%m%d).tar.gz /data
```

## 🌐 Production Considerations

### SSL/HTTPS Setup
1. Obtain SSL certificates (Let's Encrypt recommended)
2. Place certificates in `docker/nginx/ssl/`
3. Update nginx configuration for HTTPS
4. Configure domain names

### Performance Optimization
- Enable Redis caching
- Configure resource limits
- Use production logging levels
- Implement log rotation

### Monitoring and Alerting
- Set up Grafana dashboards
- Configure alert rules
- Monitor resource usage
- Set up log aggregation

## 📋 Environment Variables

### Required Variables
```env
# Bot tokens (all required)
BOT_TOKEN=your_main_bot_token
ADMIN_BOT_TOKEN=your_admin_bot_token
FINANCE_BOT_TOKEN=your_finance_bot_token
MAINTENANCE_BOT_TOKEN=your_maintenance_bot_token
MANAGEMENT_BOT_TOKEN=your_management_bot_token
ORDER_TRACK_BOT_TOKEN=your_order_track_bot_token
DELIVERY_BOT_TOKEN=your_delivery_bot_token

# Firebase configuration
FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com/
FIREBASE_CREDENTIALS_FILE=./wiz-aroma-firebase-adminsdk.json

# System authorization
SYSTEM_ADMIN_ID=your_telegram_user_id
```

### Optional Variables
```env
# Application settings
TEST_MODE=false
LOG_LEVEL=INFO

# Redis password
REDIS_PASSWORD=secure_password_here

# Monitoring passwords
GRAFANA_PASSWORD=secure_password_here
```

## 🆘 Support

### Getting Help
1. Check the logs: `./deploy.sh logs`
2. Verify configuration: Review `.env` file
3. Check service status: `./deploy.sh status`
4. Review documentation: `DOCKER_DEPLOYMENT.md`

### Useful Resources
- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose Reference](https://docs.docker.com/compose/)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Telegram Bot API](https://core.telegram.org/bots/api)

---

**Note**: This Docker setup maintains all functionality of the original Wiz-Aroma application while providing containerized deployment, monitoring, and security features.
