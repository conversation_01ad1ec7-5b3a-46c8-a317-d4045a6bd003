#!/bin/bash
# Wiz-Aroma Docker Deployment Script
# Quick deployment script for the Wiz-Aroma Food Delivery System

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="wiz-aroma"
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Help function
show_help() {
    cat << EOF
Wiz-Aroma Docker Deployment Script

Usage: $0 [COMMAND] [OPTIONS]

Commands:
    setup       Initial setup and configuration
    start       Start all services
    stop        Stop all services
    restart     Restart all services
    logs        Show logs
    status      Show service status
    update      Update and restart services
    backup      Create data backup
    clean       Clean up containers and volumes (DESTRUCTIVE)
    monitor     Start with monitoring stack
    security    Start with security hardening

Options:
    -h, --help      Show this help message
    -v, --verbose   Verbose output
    -f, --force     Force operation without confirmation

Examples:
    $0 setup           # Initial setup
    $0 start           # Start services
    $0 logs -f         # Follow logs
    $0 monitor         # Start with monitoring
    $0 backup          # Create backup
EOF
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check if Docker is running
    if ! docker info &> /dev/null; then
        error "Docker is not running. Please start Docker first."
    fi
    
    log "Prerequisites check passed"
}

# Setup function
setup() {
    log "Starting Wiz-Aroma setup..."
    
    # Check if .env exists
    if [[ ! -f "$ENV_FILE" ]]; then
        if [[ -f ".env.docker" ]]; then
            log "Copying .env.docker to .env"
            cp .env.docker .env
            warn "Please edit .env file with your actual credentials before starting services"
        else
            error ".env.docker template not found. Please ensure you have the complete project files."
        fi
    fi
    
    # Create necessary directories
    log "Creating directories..."
    mkdir -p logs data_files docker/nginx/ssl docker/secrets
    
    # Set permissions
    chmod 750 logs data_files
    chmod 700 docker/secrets
    
    # Run security setup if available
    if [[ -f "docker/security/security-setup.sh" ]]; then
        log "Running security setup..."
        chmod +x docker/security/security-setup.sh
        ./docker/security/security-setup.sh
    fi
    
    log "Setup completed. Please configure your .env file and then run: $0 start"
}

# Start services
start_services() {
    log "Starting Wiz-Aroma services..."
    
    # Check if .env is configured
    if grep -q "your_.*_token_here" .env 2>/dev/null; then
        error "Please configure your .env file with actual credentials before starting services"
    fi
    
    # Build and start services
    docker-compose up -d --build
    
    log "Services started successfully"
    show_status
}

# Stop services
stop_services() {
    log "Stopping Wiz-Aroma services..."
    docker-compose down
    log "Services stopped"
}

# Restart services
restart_services() {
    log "Restarting Wiz-Aroma services..."
    docker-compose down
    docker-compose up -d --build
    log "Services restarted"
    show_status
}

# Show logs
show_logs() {
    if [[ "$1" == "-f" ]] || [[ "$1" == "--follow" ]]; then
        docker-compose logs -f
    else
        docker-compose logs --tail=100
    fi
}

# Show status
show_status() {
    log "Service Status:"
    docker-compose ps
    
    log "Health Status:"
    docker-compose ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"
}

# Update services
update_services() {
    log "Updating Wiz-Aroma services..."
    
    # Pull latest changes if in git repo
    if [[ -d ".git" ]]; then
        log "Pulling latest changes..."
        git pull origin main || warn "Failed to pull latest changes"
    fi
    
    # Rebuild and restart
    docker-compose down
    docker-compose build --no-cache
    docker-compose up -d
    
    log "Services updated successfully"
    show_status
}

# Create backup
create_backup() {
    log "Creating backup..."
    
    BACKUP_DIR="backups"
    BACKUP_FILE="wiz-aroma-backup-$(date +%Y%m%d_%H%M%S).tar.gz"
    
    mkdir -p "$BACKUP_DIR"
    
    # Backup data volumes
    docker run --rm \
        -v wiz-aroma-logs:/data/logs \
        -v wiz-aroma-data:/data/data_files \
        -v "$(pwd)/$BACKUP_DIR":/backup \
        alpine tar czf "/backup/$BACKUP_FILE" /data
    
    log "Backup created: $BACKUP_DIR/$BACKUP_FILE"
}

# Clean up (destructive)
clean_up() {
    warn "This will remove all containers, volumes, and data. This action cannot be undone!"
    read -p "Are you sure you want to continue? (yes/no): " -r
    
    if [[ $REPLY == "yes" ]]; then
        log "Cleaning up..."
        docker-compose down -v --remove-orphans
        docker system prune -f
        log "Cleanup completed"
    else
        log "Cleanup cancelled"
    fi
}

# Start with monitoring
start_monitoring() {
    log "Starting Wiz-Aroma with monitoring stack..."
    
    if [[ -f "docker/monitoring/docker-compose.monitoring.yml" ]]; then
        docker-compose -f docker-compose.yml -f docker/monitoring/docker-compose.monitoring.yml up -d --build
        log "Services with monitoring started successfully"
        info "Grafana: http://localhost:3000 (admin/admin123)"
        info "Prometheus: http://localhost:9090"
    else
        error "Monitoring configuration not found"
    fi
}

# Start with security
start_security() {
    log "Starting Wiz-Aroma with security hardening..."
    
    if [[ -f "docker/security/docker-compose.security.yml" ]]; then
        docker-compose -f docker-compose.yml -f docker/security/docker-compose.security.yml up -d --build
        log "Services with security hardening started successfully"
    else
        error "Security configuration not found"
    fi
}

# Main function
main() {
    case "${1:-}" in
        setup)
            check_prerequisites
            setup
            ;;
        start)
            check_prerequisites
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            check_prerequisites
            restart_services
            ;;
        logs)
            show_logs "${2:-}"
            ;;
        status)
            show_status
            ;;
        update)
            check_prerequisites
            update_services
            ;;
        backup)
            create_backup
            ;;
        clean)
            clean_up
            ;;
        monitor)
            check_prerequisites
            start_monitoring
            ;;
        security)
            check_prerequisites
            start_security
            ;;
        -h|--help|help)
            show_help
            ;;
        *)
            error "Unknown command: ${1:-}. Use '$0 --help' for usage information."
            ;;
    esac
}

# Run main function with all arguments
main "$@"
