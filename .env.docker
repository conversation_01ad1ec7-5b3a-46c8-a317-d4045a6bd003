# ===============================================================================
# WIZ-AROMA FOOD DELIVERY SYSTEM v2.0 - DOCKER ENVIRONMENT CONFIGURATION
# ===============================================================================
# This file contains environment variables for Docker deployment.
# Copy this file to .env and fill in your actual values.
# NEVER commit the actual .env file with real credentials to version control!

# ===============================================================================
# TELEGRAM BOT TOKENS (REQUIRED)
# ===============================================================================
# All bot tokens are required for the multi-bot system to function properly.
# Get these tokens from @BotFather on Telegram.

# Main customer-facing bot
BOT_TOKEN=your_main_bot_token_here

# Administrative bots
ADMIN_BOT_TOKEN=your_admin_bot_token_here
FINANCE_BOT_TOKEN=your_finance_bot_token_here
MAINTENANCE_BOT_TOKEN=your_maintenance_bot_token_here

# Specialized operational bots
MANAGEMENT_BOT_TOKEN=your_management_bot_token_here
ORDER_TRACK_BOT_TOKEN=your_order_track_bot_token_here
DELIVERY_BOT_TOKEN=your_delivery_bot_token_here

# ===============================================================================
# FIREBASE CONFIGURATION (REQUIRED)
# ===============================================================================
# Firebase is the exclusive data storage backend for the system.

# Firebase Realtime Database URL
FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com/

# Firebase credentials file path (relative to container)
# Place your Firebase service account JSON file in the project root
FIREBASE_CREDENTIALS_FILE=./wiz-aroma-firebase-adminsdk.json

# ===============================================================================
# SYSTEM AUTHORIZATION (REQUIRED)
# ===============================================================================
# Telegram user IDs for system access control and security.

# Primary system administrator Telegram ID (REQUIRED)
SYSTEM_ADMIN_ID=your_telegram_user_id_here

# Authorized users for specialized bots (JSON array format)
ORDER_TRACK_BOT_AUTHORIZED_IDS=[your_telegram_user_id_here]
DELIVERY_BOT_AUTHORIZED_IDS=[your_telegram_user_id_here, another_user_id]
MANAGEMENT_BOT_AUTHORIZED_IDS=[your_telegram_user_id_here]

# ===============================================================================
# PAYMENT INFORMATION (REQUIRED)
# ===============================================================================
# Payment account details displayed to customers during checkout.

# Telebirr Mobile Payment
TELEBIRR_PHONE=**********
TELEBIRR_NAME=Your Business Name

# Commercial Bank of Ethiopia (CBE)
CBE_ACCOUNT_NUMBER=****************
CBE_ACCOUNT_NAME=Your Business Name

# Bank of Abyssinia (BOA)
BOA_ACCOUNT_NUMBER=****************
BOA_ACCOUNT_NAME=Your Business Name

# ===============================================================================
# CONTACT & SUPPORT INFORMATION (REQUIRED)
# ===============================================================================
# Contact details displayed to customers for support and inquiries.

# Primary support phone numbers
SUPPORT_PHONE_1=+************
SUPPORT_PHONE_2=+************

# Telegram support handle (without @)
SUPPORT_TELEGRAM=your_support_handle

# Customer service contact information
CUSTOMER_SERVICE_PHONE=+************
CUSTOMER_SERVICE_EMAIL=<EMAIL>
BUSINESS_EMAIL=<EMAIL>

# ===============================================================================
# EMAIL CONFIGURATION (OPTIONAL)
# ===============================================================================
# Email settings for order notifications and system alerts.

EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your_app_specific_password_here

# ===============================================================================
# DOCKER-SPECIFIC CONFIGURATION
# ===============================================================================
# Configuration specific to Docker deployment.

# Application settings
TEST_MODE=false
LOG_LEVEL=INFO

# Redis configuration (for caching)
REDIS_PASSWORD=wizaroma2024_change_this_password

# Build configuration
BUILD_DATE=2024-01-01T00:00:00Z

# ===============================================================================
# SECURITY NOTES
# ===============================================================================
# 1. Change all default passwords and tokens
# 2. Use strong, unique passwords for all services
# 3. Regularly rotate credentials
# 4. Monitor access logs for suspicious activity
# 5. Keep Firebase security rules properly configured
# 6. Use HTTPS in production environments
